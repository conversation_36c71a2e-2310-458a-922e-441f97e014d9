# Google OAuth Setup Guide

This guide explains how to fix the Google Sign-In errors you're experiencing and properly configure Google OAuth for both web and mobile platforms.

## Issues Fixed

1. **"client_secret is missing" error** - Fixed by using Supabase's OAuth flow instead of manual token exchange
2. **Cross-Origin-Opener-Policy error** - Fixed by using Supabase's built-in OAuth handling which properly manages CORS

## Required Setup Steps

### 1. Google Cloud Console Configuration

You need to configure your Google OAuth credentials properly:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project or create a new one
3. Enable the Google+ API and Google OAuth2 API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"

Create **separate** OAuth clients for different platforms:

#### Web Application Client
- **Application type**: Web application
- **Name**: WhereZ Web Client
- **Authorized JavaScript origins**: 
  - `http://localhost:8082`
  - `http://localhost:8081`
  - `http://localhost:3000`
  - Your production domain (when deployed)
- **Authorized redirect URIs**:
  - `http://localhost:8082/auth/callback`
  - `http://localhost:8081/auth/callback`
  - `http://localhost:3000/auth/callback`
  - Your production domain + `/auth/callback`

#### iOS Application Client
- **Application type**: iOS
- **Name**: WhereZ iOS Client
- **Bundle ID**: `com.wheresz.mobile`

#### Android Application Client
- **Application type**: Android
- **Name**: WhereZ Android Client
- **Package name**: `com.wheresz.mobile`
- **SHA-1 certificate fingerprint**: (Get from your keystore)

### 2. Update Environment Variables

Update your `frontend/.env` file:

```env
# Google OAuth - Use the client IDs from Google Cloud Console
GOOGLE_CLIENT_ID_WEB=your-web-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_ID_IOS=your-ios-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_ID_ANDROID=your-android-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-web-client-secret
```

### 3. Update Supabase Configuration

Update your `supabase/.env` file:

```env
# Use the WEB client credentials for Supabase OAuth
GOOGLE_CLIENT_ID=your-web-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-web-client-secret
```

### 4. Configure Supabase Dashboard (Production)

For production, you'll need to configure OAuth in your Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to Authentication → Providers
3. Enable Google provider
4. Add your Google OAuth credentials:
   - **Client ID**: Your web client ID
   - **Client Secret**: Your web client secret
5. Configure redirect URLs to include your production domain

## How the Fix Works

### Web Platform
- Uses Supabase's built-in OAuth flow (`signInWithOAuth`)
- Redirects to Google's OAuth page
- Google redirects back to your configured callback URL
- Supabase handles the token exchange automatically
- Auth state is managed via Supabase's auth state listener

### Mobile Platform
- iOS/Android continue to use the native Google Sign-In SDK
- Uses platform-specific client IDs
- Falls back to Supabase auth after getting the ID token

## Testing the Fix

1. Start your development servers:
   ```bash
   # Terminal 1: Start Supabase
   cd supabase && npx supabase start
   
   # Terminal 2: Start frontend
   cd frontend && npx expo start --web
   ```

2. Open the web app in your browser
3. Try signing in with Google
4. You should be redirected to Google's OAuth page
5. After authorization, you'll be redirected back and signed in

## Troubleshooting

### Still getting "client_secret is missing"?
- Ensure you're using the correct web client ID and secret in Supabase configuration
- Verify the redirect URIs match exactly in Google Cloud Console

### Cross-Origin errors?
- Make sure your localhost URLs are added to "Authorized JavaScript origins" in Google Cloud Console
- Ensure Supabase is using the correct site_url and additional_redirect_urls

### OAuth redirect not working?
- Check that the callback URL (`/auth/callback`) is properly configured
- Verify the `auth/callback.html` file exists in your `public` directory

## Production Deployment

When deploying to production:

1. Add your production domain to Google Cloud Console OAuth configuration
2. Update Supabase project settings with production OAuth credentials
3. Update environment variables with production values
4. Ensure HTTPS is enabled for OAuth to work properly

## Security Notes

- Never commit OAuth client secrets to version control
- Use environment variables for all sensitive credentials
- Regularly rotate OAuth credentials
- Monitor OAuth usage in Google Cloud Console

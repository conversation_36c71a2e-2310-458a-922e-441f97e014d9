# WhereZ MVP Mobile Application

A complete mobile application MVP with React Native frontend and FastAPI backend, featuring OAuth authentication, image upload, and Q&A functionality.

## Project Structure

```
wheresz/
├── README.md                 # Main project documentation
├── docs/                     # API documentation and specifications
│   └── api-spec.yaml        # OpenAPI/Swagger specification
├── backend/                  # Python FastAPI backend
│   ├── app/                 # Main application code
│   ├── tests/               # Backend tests
│   ├── requirements.txt     # Python dependencies
│   ├── .env.example        # Environment variables example
│   └── README.md           # Backend setup instructions
├── frontend/                # React Native/Expo frontend
│   ├── src/                # Source code
│   ├── assets/             # Images, fonts, etc.
│   ├── __tests__/          # Frontend tests
│   ├── package.json        # Node.js dependencies
│   ├── .env.example       # Environment variables example
│   └── README.md          # Frontend setup instructions
└── .gitignore             # Git ignore file
```

## Features

### Authentication System
- OAuth2 integration for Apple Sign-In, Google Sign-In, and Microsoft Sign-In
- JWT token-based session management
- User profile management

### Image Upload Functionality
- Upload images from camera or photo library
- Image compression and validation
- Image storage and display

### Question & Answer System
- Text and audio input for questions
- Text and image-based answers
- Question/answer history

## 🚀 Quick Start

### Deployment Options

**🌟 Option 1: Supabase Deployment (Recommended)**
- Complete backend-as-a-service solution
- Built-in authentication, database, and storage
- Automatic scaling and backups
- **See [SUPABASE_DEPLOYMENT.md](./SUPABASE_DEPLOYMENT.md) for detailed guide**

**⚙️ Option 2: Custom Backend Deployment**
- Self-hosted FastAPI backend
- Full control over infrastructure

### Prerequisites
- Node.js 18+ and npm/yarn
- Expo CLI for mobile development
- Supabase account (for Option 1) OR Python 3.9+ (for Option 2)

### Option 1: Supabase Setup (Recommended)

```bash
# 1. Create Supabase project at supabase.com
# 2. Clone and setup
git clone <repository-url>
cd wheresz

# 3. Install Supabase CLI and setup
npm install -g supabase
supabase init
supabase link --project-ref <your-project-ref>
supabase db push

# 4. Frontend setup
cd frontend
npm install
cp .env.example .env
# Add your Supabase URL and keys to .env
npx expo start
```

### Option 2: Custom Backend Setup

```bash
# Backend setup
cd backend
pip install -r requirements.txt
cp .env.example .env
# Configure environment variables
python -m uvicorn app.main:app --reload

# Frontend setup
cd frontend
npm install
cp .env.example .env
# Configure environment variables
npx expo start
```

## 📚 Documentation

- **[🚀 Supabase Deployment Guide](SUPABASE_DEPLOYMENT.md)** - Complete Supabase setup and deployment
- [API Documentation](docs/api-spec.yaml) - Complete OpenAPI specification
- [Backend README](backend/README.md) - Custom backend setup and deployment
- [Frontend README](frontend/README.md) - Frontend setup and development

## Technology Stack

### Backend
- **Primary**: Supabase (PostgreSQL + Auth + Storage + Real-time)
- **Alternative**: FastAPI with SQLAlchemy ORM
- **Database**: PostgreSQL with Row Level Security
- **Authentication**: OAuth2 (Apple, Google, Microsoft) + JWT
- **Language**: Python 3.9+ with TypeScript-style type hints

### Frontend
- **Framework**: React Native with Expo SDK 49
- **Language**: TypeScript
- **Navigation**: React Navigation 6
- **State Management**: React Context + Hooks
- **HTTP Client**: Axios + Supabase Client
- **Authentication**: Expo Auth Session + Supabase Auth

## Development

### Environment Variables

Both frontend and backend require environment configuration. Copy the `.env.example` files and configure:

- OAuth provider credentials (Apple, Google, Microsoft)
- Database connection strings
- JWT secrets
- Image storage configuration

### Testing

```bash
# Backend tests
cd backend && python -m pytest

# Frontend tests
cd frontend && npm test
```

## Deployment

### Backend
- Deploy to cloud platforms (AWS, GCP, Azure)
- Configure PostgreSQL database
- Set up environment variables
- Enable HTTPS

### Frontend
- Build with Expo EAS Build
- Deploy to App Store and Google Play Store
- Configure deep linking and push notifications

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Submit a pull request


# WhereZ - Local Development Guide

This guide will help you set up the WhereZ application for local development across all components: backend, frontend (mobile app), and database.

## 🏗️ Architecture Overview

WhereZ is a full-stack mobile application with:
- **Backend**: FastAPI with Supabase integration (PostgreSQL + Auth + Storage)
- **Frontend**: React Native with Expo (TypeScript)
- **Database**: PostgreSQL with Supabase (local development via Supabase CLI)
- **Authentication**: OAuth2 (Apple, Google, Microsoft) + JWT
- **Storage**: Supabase Storage for images/files

## 📋 Prerequisites

### System Requirements
- **Node.js**: 18+ and npm/yarn
- **Python**: 3.9+
- **Git**: Latest version
- **Docker**: For local Supabase (optional but recommended)

### CLI Tools
```bash
# Install required CLI tools
npm install -g @expo/cli
npm install -g supabase
pip install --upgrade pip
```

### Development Tools (Recommended)
- **iOS**: Xcode + iOS Simulator (macOS only)
- **Android**: Android Studio + Android Emulator
- **Mobile**: Expo Go app on physical device
- **Code Editor**: VS Code with extensions:
  - Python
  - TypeScript and JavaScript
  - React Native Tools
  - Expo Tools

## 🚀 Quick Start

### 1. Clone and Setup Repository

```bash
# Clone the repository
git clone <your-repo-url>
cd wheresz

# Make setup script executable
chmod +x setup-supabase.sh
```

### 2. Database Setup (Supabase Local)

```bash
# Start local Supabase (includes PostgreSQL, Auth, Storage, etc.)
supabase start

# This will:
# - Download and start Docker containers
# - Set up local PostgreSQL database
# - Start Supabase services (Auth, Storage, API)
# - Apply migrations from supabase/migrations/
```

**Important**: The first run will take several minutes to download Docker images.

After successful start, you'll see output like:
```
Started supabase local development setup.

         API URL: http://localhost:54321
          DB URL: postgresql://postgres:postgres@localhost:54322/postgres
      Studio URL: http://localhost:54323
    Inbucket URL: http://localhost:54324
        anon key: eyJ0eXAiOiJKV1Q...
service_role key: eyJ0eXAiOiJKV1Q...
```

### 3. Backend Setup

```bash
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate
# On Windows:
# venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Create environment file
cp .env.example .env
```

**Configure backend/.env**:
```env
# Database (Local Supabase)
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres
DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@localhost:54322/postgres

# Supabase Configuration (from supabase start output)
SUPABASE_URL=http://localhost:54321
USE_SUPABASE=true

# JWT Configuration
JWT_SECRET_KEY=your-local-development-secret-key
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# OAuth Configuration (for testing - get from provider consoles)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
APPLE_CLIENT_ID=your.app.bundle.id
MICROSOFT_CLIENT_ID=your-microsoft-client-id

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB

# Development
DEBUG=true
```

**Start the backend**:
```bash
# Run database migrations (if needed)
alembic upgrade head

# Start the development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

The backend API will be available at: `http://localhost:8000`
API documentation: `http://localhost:8000/docs`

### 4. Frontend Setup

```bash
cd frontend

# Install dependencies
npm install

# Create environment file
cp .env.example .env
```

**Configure frontend/.env**:
```env
# Supabase Configuration (from supabase start output)
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1Q...  # Use anon key from supabase start
USE_SUPABASE=true

# API Configuration (for hybrid mode)
API_BASE_URL=http://localhost:8000

# OAuth Configuration (for testing)
GOOGLE_CLIENT_ID=your-google-client-id
APPLE_CLIENT_ID=your.app.bundle.id
MICROSOFT_CLIENT_ID=your-microsoft-client-id
```

**Start the mobile app**:
```bash
# Start Expo development server
npx expo start

# Or use specific platform
npx expo start --ios      # iOS simulator
npx expo start --android  # Android emulator
npx expo start --web      # Web browser
```

## 🔧 Development Workflow

### Daily Development

1. **Start Supabase** (if not running):
   ```bash
   supabase start
   ```

2. **Start Backend** (in backend/ directory):
   ```bash
   source venv/bin/activate  # Activate virtual environment
   uvicorn app.main:app --reload
   ```

3. **Start Frontend** (in frontend/ directory):
   ```bash
   npx expo start
   ```

### Database Management

**View local database**:
```bash
# Open Supabase Studio (web interface)
open http://localhost:54323

# Or connect directly to PostgreSQL
psql postgresql://postgres:postgres@localhost:54322/postgres
```

**Reset database** (careful - deletes all data):
```bash
supabase db reset
```

**Create new migration**:
```bash
supabase migration new your_migration_name
# Edit the generated file in supabase/migrations/
supabase db push
```

### Testing

**Backend tests**:
```bash
cd backend
python -m pytest
python -m pytest --coverage  # With coverage report
```

**Frontend tests**:
```bash
cd frontend
npm test
npm run test:coverage  # With coverage report
```

### Code Quality

**Backend linting**:
```bash
cd backend
black .  # Format code
flake8 .  # Lint code
mypy .   # Type checking
```

**Frontend linting**:
```bash
cd frontend
npm run lint
npm run lint:fix  # Auto-fix issues
npm run type-check  # TypeScript checking
```

## 🔍 Debugging

### Common Issues

1. **Supabase won't start**:
   - Ensure Docker is running
   - Check port conflicts (54321-54324)
   - Run `supabase stop` then `supabase start`

2. **Backend database connection errors**:
   - Verify Supabase is running (`supabase status`)
   - Check DATABASE_URL in backend/.env
   - Ensure migrations are applied (`alembic upgrade head`)

3. **Frontend can't connect to backend**:
   - Verify backend is running on port 8000
   - Check API_BASE_URL in frontend/.env
   - For physical device testing, use your computer's IP address

4. **OAuth authentication issues**:
   - Configure OAuth providers in Supabase Studio
   - Set correct redirect URLs
   - Verify client IDs and secrets

### Useful Commands

```bash
# Check Supabase status
supabase status

# View Supabase logs
supabase logs

# Stop all Supabase services
supabase stop

# Backend database shell
cd backend && python -c "from app.database import engine; print(engine.url)"

# Frontend clear cache
cd frontend && npx expo start --clear
```

## 📱 Device Testing

### iOS (macOS only)
```bash
cd frontend
npx expo start --ios
```

### Android
```bash
cd frontend
npx expo start --android
```

### Physical Device
1. Install Expo Go app from App Store/Play Store
2. Start development server: `npx expo start`
3. Scan QR code with Expo Go app

**Note**: For physical device testing with local backend, update API_BASE_URL to your computer's IP address (e.g., `http://*************:8000`).

## 🔐 OAuth Setup for Development

### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create project and enable Google+ API
3. Create OAuth 2.0 credentials
4. Add redirect URLs in Supabase Studio

### Apple Sign-In
1. Apple Developer Account required
2. Configure App ID with Sign In with Apple capability
3. Create Service ID for web authentication
4. Configure in Supabase Studio

### Microsoft OAuth
1. Go to [Azure Portal](https://portal.azure.com/)
2. Register application in Azure AD
3. Configure redirect URLs
4. Add credentials to Supabase Studio

## 📚 Additional Resources

- [Supabase Local Development](https://supabase.com/docs/guides/cli/local-development)
- [Expo Development Workflow](https://docs.expo.dev/workflow/development-mode/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Native Documentation](https://reactnative.dev/docs/getting-started)

## 🆘 Getting Help

If you encounter issues:
1. Check this guide's troubleshooting section
2. Review logs: `supabase logs` and backend/frontend console output
3. Verify environment variables are correctly set
4. Ensure all services are running (`supabase status`)

Happy coding! 🚀

import * as AppleAuthentication from 'expo-apple-authentication';
import * as AuthSession from 'expo-auth-session';
import * as WebBrowser from 'expo-web-browser';
import { Platform } from 'react-native';

// Platform-specific imports to prevent web crashes
let GoogleSignin: any = null;
if (Platform.OS !== 'web') {
  try {
    const GoogleSigninModule = require('@react-native-google-signin/google-signin');
    GoogleSignin = GoogleSigninModule.GoogleSignin;
  } catch (error) {
    console.warn('Google Sign-In module not available:', error);
  }
}

import { apiService } from './api';
import { supabaseService } from './supabase';
import { OAUTH_CONFIG, FEATURES, SUPABASE_CONFIG } from '@/constants';
import type {
  AppleAuthRequest,
  AuthResponse,
  GoogleAuthRequest,
  MicrosoftAuthRequest,
} from '@/types';

// Configure WebBrowser for auth sessions
WebBrowser.maybeCompleteAuthSession();

class AuthService {
  constructor() {
    this.initializeGoogleSignIn();
  }

  private initializeGoogleSignIn() {
    if (FEATURES.GOOGLE_SIGNIN && GoogleSignin && Platform.OS !== 'web') {
      try {
        GoogleSignin.configure({
          iosClientId: OAUTH_CONFIG.GOOGLE.CLIENT_ID_IOS,
          webClientId: OAUTH_CONFIG.GOOGLE.CLIENT_ID_WEB,
          offlineAccess: true,
        });
      } catch (error) {
        console.warn('Failed to configure Google Sign-In:', error);
      }
    }
  }

  // Apple Sign-In
  async signInWithApple(): Promise<AuthResponse> {
    if (!FEATURES.APPLE_SIGNIN) {
      throw new Error('Apple Sign-In is not enabled');
    }

    // Web platform uses OAuth flow instead of native Apple Sign-In
    if (Platform.OS === 'web') {
      return this.signInWithAppleWeb();
    }

    if (Platform.OS !== 'ios') {
      throw new Error('Apple Sign-In is only available on iOS');
    }

    try {
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
      });

      // Try Supabase Auth first
      if (SUPABASE_CONFIG.USE_SUPABASE && supabaseService.isAvailable()) {
        try {
          const { session } = await supabaseService.signInWithIdToken(
            'apple',
            credential.identityToken!
          );

          if (session?.user) {
            // Convert Supabase user to our format
            const user = this.convertSupabaseUser(session.user);
            return {
              access_token: session.access_token,
              refresh_token: session.refresh_token,
              token_type: 'bearer',
              expires_in: session.expires_in || 3600,
              user,
            };
          }
        } catch (supabaseError) {
          console.warn('Supabase Apple Sign-In failed, falling back to custom backend:', supabaseError);
        }
      }

      // Fallback to custom backend
      const authRequest: AppleAuthRequest = {
        identity_token: credential.identityToken!,
        authorization_code: credential.authorizationCode!,
        user_info: credential.fullName ? {
          name: {
            firstName: credential.fullName.givenName || undefined,
            lastName: credential.fullName.familyName || undefined,
          },
          email: credential.email || undefined,
        } : undefined,
      };

      const response = await apiService.post<AuthResponse>('/auth/oauth/apple', authRequest);
      return response.data!;
    } catch (error: any) {
      if (error.code === 'ERR_CANCELED') {
        throw new Error('Apple Sign-In was canceled');
      }
      throw new Error(`Apple Sign-In failed: ${error.message}`);
    }
  }

  // Apple Sign-In for Web
  private async signInWithAppleWeb(): Promise<AuthResponse> {
    try {
      // Use Supabase OAuth for web
      if (SUPABASE_CONFIG.USE_SUPABASE && supabaseService.isAvailable()) {
        const { data, error } = await supabaseService.signInWithOAuth('apple');

        if (error) {
          throw new Error(error.message);
        }

        // The OAuth flow will redirect to the configured URL
        // The session will be handled by the auth state listener in AuthContext
        throw new Error('OAUTH_REDIRECT_INITIATED');
      }

      throw new Error('Apple Sign-In requires Supabase configuration');
    } catch (error: any) {
      throw error;
    }
  }

  // Google Sign-In
  async signInWithGoogle(): Promise<AuthResponse> {
    if (!FEATURES.GOOGLE_SIGNIN) {
      throw new Error('Google Sign-In is not enabled');
    }

    // Web platform uses OAuth flow instead of native Google Sign-In
    if (Platform.OS === 'web') {
      return this.signInWithGoogleWeb();
    }

    if (!GoogleSignin) {
      throw new Error('Google Sign-In is not available on this platform');
    }

    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();

      // Try Supabase Auth first
      if (SUPABASE_CONFIG.USE_SUPABASE && supabaseService.isAvailable()) {
        try {
          const { session } = await supabaseService.signInWithIdToken(
            'google',
            userInfo.idToken!
          );

          if (session?.user) {
            // Convert Supabase user to our format
            const user = this.convertSupabaseUser(session.user);
            return {
              access_token: session.access_token,
              refresh_token: session.refresh_token,
              token_type: 'bearer',
              expires_in: session.expires_in || 3600,
              user,
            };
          }
        } catch (supabaseError) {
          console.warn('Supabase Google Sign-In failed, falling back to custom backend:', supabaseError);
        }
      }

      // Fallback to custom backend
      const authRequest: GoogleAuthRequest = {
        id_token: userInfo.idToken!,
        access_token: userInfo.serverAuthCode || undefined,
      };

      const response = await apiService.post<AuthResponse>('/auth/oauth/google', authRequest);
      return response.data!;
    } catch (error: any) {
      if (error.code === 'SIGN_IN_CANCELLED') {
        throw new Error('Google Sign-In was canceled');
      }
      throw new Error(`Google Sign-In failed: ${error.message}`);
    }
  }

  // Google Sign-In for Web
  private async signInWithGoogleWeb(): Promise<AuthResponse> {
    try {
      // Use Supabase OAuth for web to avoid CORS issues
      if (SUPABASE_CONFIG.USE_SUPABASE && supabaseService.isAvailable()) {
        const { data, error } = await supabaseService.signInWithOAuth('google');

        if (error) {
          throw new Error(error.message);
        }

        // The OAuth flow will redirect to the configured URL
        // The session will be handled by the auth state listener in AuthContext
        // We throw a special error to indicate the flow was initiated
        throw new Error('OAUTH_REDIRECT_INITIATED');
      }

      // Fallback to manual OAuth flow if Supabase is not available
      const discovery = {
        authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
        tokenEndpoint: 'https://oauth2.googleapis.com/token',
      };

      // Use a more specific redirect URI for web
      const redirectUri = Platform.OS === 'web'
        ? `${window.location.origin}/auth/callback`
        : AuthSession.makeRedirectUri({ useProxy: true });

      const request = new AuthSession.AuthRequest({
        clientId: OAUTH_CONFIG.GOOGLE.CLIENT_ID_WEB,
        scopes: ['openid', 'profile', 'email'],
        redirectUri,
        responseType: AuthSession.ResponseType.Code,
        extraParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      });

      const result = await request.promptAsync(discovery, {
        useProxy: false, // Disable proxy for web to avoid CORS issues
        showInRecents: false,
      });

      if (result.type !== 'success') {
        throw new Error('Google Sign-In was canceled or failed');
      }

      // Exchange code for tokens
      const tokenResponse = await AuthSession.exchangeCodeAsync(
        {
          clientId: OAUTH_CONFIG.GOOGLE.CLIENT_ID_WEB,
          code: result.params.code,
          redirectUri,
          extraParams: {},
        },
        discovery
      );

      // Fallback to custom backend
      const authRequest: GoogleAuthRequest = {
        id_token: tokenResponse.idToken!,
        access_token: tokenResponse.accessToken || undefined,
      };

      const response = await apiService.post<AuthResponse>('/auth/oauth/google', authRequest);
      return response.data!;
    } catch (error: any) {
      throw new Error(`Google Sign-In failed: ${error.message}`);
    }
  }

  // Microsoft Sign-In
  async signInWithMicrosoft(): Promise<AuthResponse> {
    if (!FEATURES.MICROSOFT_SIGNIN) {
      throw new Error('Microsoft Sign-In is not enabled');
    }

    // Web platform uses OAuth flow instead of manual implementation
    if (Platform.OS === 'web') {
      return this.signInWithMicrosoftWeb();
    }

    try {
      const discovery = {
        authorizationEndpoint: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
        tokenEndpoint: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
      };

      const request = new AuthSession.AuthRequest({
        clientId: OAUTH_CONFIG.MICROSOFT.CLIENT_ID,
        scopes: ['openid', 'profile', 'email'],
        redirectUri: OAUTH_CONFIG.MICROSOFT.REDIRECT_URI,
        responseType: AuthSession.ResponseType.Code,
        extraParams: {
          response_mode: 'query',
        },
      });

      const result = await request.promptAsync(discovery);

      if (result.type !== 'success') {
        throw new Error('Microsoft Sign-In was canceled or failed');
      }

      // Exchange code for tokens
      const tokenResponse = await AuthSession.exchangeCodeAsync(
        {
          clientId: OAUTH_CONFIG.MICROSOFT.CLIENT_ID,
          code: result.params.code,
          redirectUri: OAUTH_CONFIG.MICROSOFT.REDIRECT_URI,
          extraParams: {},
        },
        discovery
      );

      const authRequest: MicrosoftAuthRequest = {
        access_token: tokenResponse.accessToken,
        id_token: tokenResponse.idToken || undefined,
      };

      const response = await apiService.post<AuthResponse>('/auth/oauth/microsoft', authRequest);
      return response.data!;
    } catch (error: any) {
      throw new Error(`Microsoft Sign-In failed: ${error.message}`);
    }
  }

  // Microsoft Sign-In for Web
  private async signInWithMicrosoftWeb(): Promise<AuthResponse> {
    try {
      // Use Supabase OAuth for web
      if (SUPABASE_CONFIG.USE_SUPABASE && supabaseService.isAvailable()) {
        const { data, error } = await supabaseService.signInWithOAuth('azure');

        if (error) {
          throw new Error(error.message);
        }

        // The OAuth flow will redirect to the configured URL
        // The session will be handled by the auth state listener in AuthContext
        throw new Error('OAUTH_REDIRECT_INITIATED');
      }

      throw new Error('Microsoft Sign-In requires Supabase configuration');
    } catch (error: any) {
      throw error;
    }
  }

  // Facebook Sign-In
  async signInWithFacebook(): Promise<AuthResponse> {
    if (!FEATURES.FACEBOOK_SIGNIN) {
      throw new Error('Facebook Sign-In is not enabled');
    }

    // Web platform uses OAuth flow
    if (Platform.OS === 'web') {
      return this.signInWithFacebookWeb();
    }

    // For mobile, you would need to implement Facebook SDK
    throw new Error('Facebook Sign-In on mobile requires Facebook SDK implementation');
  }

  // Facebook Sign-In for Web
  private async signInWithFacebookWeb(): Promise<AuthResponse> {
    try {
      // Use Supabase OAuth for web
      if (SUPABASE_CONFIG.USE_SUPABASE && supabaseService.isAvailable()) {
        const { data, error } = await supabaseService.signInWithOAuth('facebook');

        if (error) {
          throw new Error(error.message);
        }

        // The OAuth flow will redirect to the configured URL
        // The session will be handled by the auth state listener in AuthContext
        throw new Error('OAUTH_REDIRECT_INITIATED');
      }

      throw new Error('Facebook Sign-In requires Supabase configuration');
    } catch (error: any) {
      throw error;
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      // Sign out from Supabase if available
      if (SUPABASE_CONFIG.USE_SUPABASE && supabaseService.isAvailable()) {
        await supabaseService.signOut();
      } else {
        // Call backend logout endpoint
        await apiService.post('/auth/logout');
      }
    } catch (error) {
      // Continue with local logout even if backend call fails
      console.warn('Logout failed:', error);
    }

    // Sign out from OAuth providers
    try {
      if (FEATURES.GOOGLE_SIGNIN && GoogleSignin && Platform.OS !== 'web') {
        await GoogleSignin.signOut();
      }
    } catch (error) {
      console.warn('Google sign out failed:', error);
    }

    // Note: Apple doesn't provide a sign out method
    // Microsoft sign out would require additional implementation
  }

  // Helper method to convert Supabase user to our User type
  convertSupabaseUser(supabaseUser: any): any {
    return {
      id: supabaseUser.id,
      email: supabaseUser.email,
      name: supabaseUser.user_metadata?.name || supabaseUser.user_metadata?.full_name,
      first_name: supabaseUser.user_metadata?.first_name || supabaseUser.user_metadata?.given_name,
      last_name: supabaseUser.user_metadata?.last_name || supabaseUser.user_metadata?.family_name,
      avatar_url: supabaseUser.user_metadata?.avatar_url || supabaseUser.user_metadata?.picture,
      provider: supabaseUser.app_metadata?.provider || 'unknown',
      created_at: supabaseUser.created_at,
      updated_at: supabaseUser.updated_at,
    };
  }

  // Check if Apple Sign-In is available
  async isAppleSignInAvailable(): Promise<boolean> {
    if (!FEATURES.APPLE_SIGNIN || Platform.OS !== 'ios') {
      return false;
    }

    try {
      return await AppleAuthentication.isAvailableAsync();
    } catch {
      return false;
    }
  }

  // Check if Google Sign-In is available
  async isGoogleSignInAvailable(): Promise<boolean> {
    if (!FEATURES.GOOGLE_SIGNIN) {
      console.log('Google Sign-In is not enabled');
      return false;
    }

    // Web platform doesn't support Google Play Services
    if (Platform.OS === 'web') {
      return true; // Web can use OAuth flow
    }

    if (!GoogleSignin) {
      return false;
    }

    try {
      return await GoogleSignin.hasPlayServices();
    } catch {
      return false;
    }
  }

  // Check if Microsoft Sign-In is available
  isMicrosoftSignInAvailable(): boolean {
    return FEATURES.MICROSOFT_SIGNIN && !!OAUTH_CONFIG.MICROSOFT.CLIENT_ID;
  }

  // Check if Facebook Sign-In is available
  isFacebookSignInAvailable(): boolean {
    return FEATURES.FACEBOOK_SIGNIN && !!OAUTH_CONFIG.FACEBOOK.CLIENT_ID;
  }

  // Handle OAuth callback for web (used after redirect from OAuth provider)
  async handleOAuthCallback(): Promise<AuthResponse | null> {
    if (Platform.OS !== 'web' || !SUPABASE_CONFIG.USE_SUPABASE || !supabaseService.isAvailable()) {
      return null;
    }

    try {
      // Get the current session after OAuth redirect
      const { data: { session }, error } = await supabaseService.getSession();

      if (error) {
        throw new Error(error.message);
      }

      if (session?.user) {
        // Convert Supabase user to our format
        const user = this.convertSupabaseUser(session.user);
        return {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          token_type: 'bearer',
          expires_in: session.expires_in || 3600,
          user,
        };
      }

      return null;
    } catch (error: any) {
      console.error('OAuth callback handling failed:', error);
      return null;
    }
  }

  // Initialize OAuth session listener for web
  initializeOAuthListener(onAuthStateChange: (session: any) => void) {
    if (Platform.OS !== 'web' || !SUPABASE_CONFIG.USE_SUPABASE || !supabaseService.isAvailable()) {
      return null;
    }

    // Listen for auth state changes
    return supabaseService.onAuthStateChange((event, session) => {
      onAuthStateChange(session);
    });
  }
}

// Export singleton instance
export const authService = new AuthService();

import React, { createContext, useContext, useEffect, useReducer, ReactNode } from 'react';
import { Platform } from 'react-native';

import { authService } from '@/services/auth';
import { supabaseService } from '@/services/supabase';
import { StorageService } from '@/utils/storage';
import { STORAGE_KEYS, SUPABASE_CONFIG } from '@/constants';
import type { User, AuthResponse } from '@/types';

// Auth State
interface AuthState {
  isLoading: boolean;
  isAuthenticated: boolean;
  user: User | null;
  error: string | null;
}

// Auth Actions
type AuthAction =
  | { type: 'LOADING' }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User } }
  | { type: 'LOGIN_ERROR'; payload: { error: string } }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'UPDATE_USER'; payload: { user: User } };

// Auth Context
interface AuthContextType extends AuthState {
  signInWithApple: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithMicrosoft: () => Promise<void>;
  signInWithFacebook: () => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  updateUser: (user: User) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Use shared storage service for web compatibility
const getStorageItem = StorageService.getItem;
const setStorageItem = StorageService.setItem;
const deleteStorageItem = StorageService.removeItem;

// Auth Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'LOADING':
      return {
        ...state,
        isLoading: true,
        error: null,
      };

    case 'LOGIN_SUCCESS':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: true,
        user: action.payload.user,
        error: null,
      };

    case 'LOGIN_ERROR':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        error: action.payload.error,
      };

    case 'LOGOUT':
      return {
        ...state,
        isLoading: false,
        isAuthenticated: false,
        user: null,
        error: null,
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };

    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload.user,
      };

    default:
      return state;
  }
}

// Initial State
const initialState: AuthState = {
  isLoading: true,
  isAuthenticated: false,
  user: null,
  error: null,
};

// Auth Provider
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing authentication on app start
  useEffect(() => {
    checkAuthState();

    // Initialize OAuth listener for web
    if (Platform.OS === 'web') {
      const unsubscribe = authService.initializeOAuthListener(async (session) => {
        if (session?.user) {
          try {
            const authResponse: AuthResponse = {
              access_token: session.access_token,
              refresh_token: session.refresh_token,
              token_type: 'bearer',
              expires_in: session.expires_in || 3600,
              user: authService.convertSupabaseUser(session.user),
            };
            await storeAuthData(authResponse);
            dispatch({ type: 'LOGIN_SUCCESS', payload: { user: authResponse.user } });
          } catch (error) {
            console.error('OAuth session handling error:', error);
          }
        } else if (session === null) {
          // User signed out
          await clearAuthData();
          dispatch({ type: 'LOGOUT' });
        }
      });

      return () => {
        if (unsubscribe) {
          unsubscribe.subscription?.unsubscribe();
        }
      };
    }
  }, []);

  const checkAuthState = async () => {
    try {
      dispatch({ type: 'LOADING' });

      // First check for Supabase session if using Supabase
      if (SUPABASE_CONFIG.USE_SUPABASE && supabaseService.isAvailable()) {
        const session = await supabaseService.getSession();
        if (session?.user) {
          // Convert Supabase session to our auth format
          const user = authService.convertSupabaseUser(session.user);
          const authResponse: AuthResponse = {
            access_token: session.access_token,
            refresh_token: session.refresh_token,
            token_type: 'bearer',
            expires_in: session.expires_in || 3600,
            user,
          };

          await storeAuthData(authResponse);
          dispatch({ type: 'LOGIN_SUCCESS', payload: { user } });
          return;
        }
      }

      // Fallback to stored tokens
      const [accessToken, userData] = await Promise.all([
        getStorageItem(STORAGE_KEYS.ACCESS_TOKEN),
        getStorageItem(STORAGE_KEYS.USER_DATA),
      ]);

      if (accessToken && userData) {
        const user: User = JSON.parse(userData);
        dispatch({ type: 'LOGIN_SUCCESS', payload: { user } });
      } else {
        dispatch({ type: 'LOGOUT' });
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
      dispatch({ type: 'LOGOUT' });
    }
  };

  const storeAuthData = async (authResponse: AuthResponse) => {
    await Promise.all([
      setStorageItem(STORAGE_KEYS.ACCESS_TOKEN, authResponse.access_token),
      setStorageItem(STORAGE_KEYS.REFRESH_TOKEN, authResponse.refresh_token),
      setStorageItem(STORAGE_KEYS.USER_DATA, JSON.stringify(authResponse.user)),
    ]);
  };

  const clearAuthData = async () => {
    await Promise.all([
      deleteStorageItem(STORAGE_KEYS.ACCESS_TOKEN),
      deleteStorageItem(STORAGE_KEYS.REFRESH_TOKEN),
      deleteStorageItem(STORAGE_KEYS.USER_DATA),
    ]);
  };

  const signInWithApple = async () => {
    try {
      dispatch({ type: 'LOADING' });
      const authResponse = await authService.signInWithApple();
      await storeAuthData(authResponse);
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user: authResponse.user } });
    } catch (error: any) {
      // Special case for web OAuth redirect
      if (error.message === 'OAUTH_REDIRECT_INITIATED') {
        // Keep loading state - the auth state listener will handle the result
        return;
      }
      dispatch({ type: 'LOGIN_ERROR', payload: { error: error.message } });
    }
  };

  const signInWithGoogle = async () => {
    try {
      dispatch({ type: 'LOADING' });
      const authResponse = await authService.signInWithGoogle();
      await storeAuthData(authResponse);
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user: authResponse.user } });
    } catch (error: any) {
      // Special case for web OAuth redirect
      if (error.message === 'OAUTH_REDIRECT_INITIATED') {
        // Keep loading state - the auth state listener will handle the result
        // Don't show error message for OAuth redirect
        return;
      }
      dispatch({ type: 'LOGIN_ERROR', payload: { error: error.message } });
    }
  };

  const signInWithMicrosoft = async () => {
    try {
      dispatch({ type: 'LOADING' });
      const authResponse = await authService.signInWithMicrosoft();
      await storeAuthData(authResponse);
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user: authResponse.user } });
    } catch (error: any) {
      // Special case for web OAuth redirect
      if (error.message === 'OAUTH_REDIRECT_INITIATED') {
        // Keep loading state - the auth state listener will handle the result
        return;
      }
      dispatch({ type: 'LOGIN_ERROR', payload: { error: error.message } });
    }
  };

  const signInWithFacebook = async () => {
    try {
      dispatch({ type: 'LOADING' });
      const authResponse = await authService.signInWithFacebook();
      await storeAuthData(authResponse);
      dispatch({ type: 'LOGIN_SUCCESS', payload: { user: authResponse.user } });
    } catch (error: any) {
      // Special case for web OAuth redirect
      if (error.message === 'OAUTH_REDIRECT_INITIATED') {
        // Keep loading state - the auth state listener will handle the result
        return;
      }
      dispatch({ type: 'LOGIN_ERROR', payload: { error: error.message } });
    }
  };

  const logout = async () => {
    try {
      dispatch({ type: 'LOADING' });
      await authService.logout();
      await clearAuthData();
      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      // Even if logout fails, clear local data
      await clearAuthData();
      dispatch({ type: 'LOGOUT' });
    }
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const updateUser = (user: User) => {
    dispatch({ type: 'UPDATE_USER', payload: { user } });
    // Update stored user data
    setStorageItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
  };

  const contextValue: AuthContextType = {
    ...state,
    signInWithApple,
    signInWithGoogle,
    signInWithMicrosoft,
    signInWithFacebook,
    logout,
    clearError,
    updateUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Auth Hook
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

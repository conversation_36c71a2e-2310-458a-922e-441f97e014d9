import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';

/**
 * Web-compatible storage utility that uses SecureStore on native platforms
 * and localStorage on web platform.
 */
export class StorageService {
  /**
   * Get an item from storage
   */
  static async getItem(key: string): Promise<string | null> {
    if (Platform.OS === 'web') {
      return localStorage.getItem(key);
    }
    return await SecureStore.getItemAsync(key);
  }

  /**
   * Set an item in storage
   */
  static async setItem(key: string, value: string): Promise<void> {
    if (Platform.OS === 'web') {
      localStorage.setItem(key, value);
      return;
    }
    await SecureStore.setItemAsync(key, value);
  }

  /**
   * Remove an item from storage
   */
  static async removeItem(key: string): Promise<void> {
    if (Platform.OS === 'web') {
      localStorage.removeItem(key);
      return;
    }
    await SecureStore.deleteItemAsync(key);
  }

  /**
   * Check if an item exists in storage
   */
  static async hasItem(key: string): Promise<boolean> {
    const value = await this.getItem(key);
    return value !== null;
  }

  /**
   * Clear all items from storage (use with caution)
   */
  static async clear(): Promise<void> {
    if (Platform.OS === 'web') {
      localStorage.clear();
      return;
    }
    // Note: SecureStore doesn't have a clear all method
    // This would need to be implemented by tracking keys
    throw new Error('Clear all not implemented for native platforms');
  }
}

// Export default instance
export const storageService = StorageService;

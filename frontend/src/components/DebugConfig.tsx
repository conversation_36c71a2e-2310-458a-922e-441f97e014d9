import React from 'react';
import { View, Text, ScrollView } from 'react-native';
import Constants from 'expo-constants';
import { FEATURES, API_CONFIG, SUPABASE_CONFIG, OAUTH_CONFIG } from '@/constants';

export const DebugConfig: React.FC = () => {
  const extra = Constants.expoConfig?.extra || {};

  return (
    <ScrollView style={{ flex: 1, padding: 20, backgroundColor: '#f5f5f5' }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 15 }}>
        🔧 Configuration Debug Panel
      </Text>
      
      <View style={{ backgroundColor: 'white', padding: 15, marginBottom: 10, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
          Raw Extra Config:
        </Text>
        <Text style={{ fontFamily: 'monospace', fontSize: 12 }}>
          {JSON.stringify(extra, null, 2)}
        </Text>
      </View>

      <View style={{ backgroundColor: 'white', padding: 15, marginBottom: 10, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
          Feature Flags:
        </Text>
        <Text>Google Sign-In: {FEATURES.GOOGLE_SIGNIN ? '✅ Enabled' : '❌ Disabled'}</Text>
        <Text>Apple Sign-In: {FEATURES.APPLE_SIGNIN ? '✅ Enabled' : '❌ Disabled'}</Text>
        <Text>Microsoft Sign-In: {FEATURES.MICROSOFT_SIGNIN ? '✅ Enabled' : '❌ Disabled'}</Text>
        <Text>Audio Questions: {FEATURES.AUDIO_QUESTIONS ? '✅ Enabled' : '❌ Disabled'}</Text>
        <Text>Image Context: {FEATURES.IMAGE_CONTEXT ? '✅ Enabled' : '❌ Disabled'}</Text>
      </View>

      <View style={{ backgroundColor: 'white', padding: 15, marginBottom: 10, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
          API Configuration:
        </Text>
        <Text>Base URL: {API_CONFIG.BASE_URL}</Text>
        <Text>Timeout: {API_CONFIG.TIMEOUT}ms</Text>
      </View>

      <View style={{ backgroundColor: 'white', padding: 15, marginBottom: 10, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
          Supabase Configuration:
        </Text>
        <Text>URL: {SUPABASE_CONFIG.URL}</Text>
        <Text>Use Supabase: {SUPABASE_CONFIG.USE_SUPABASE ? '✅ Yes' : '❌ No'}</Text>
        <Text>Anon Key: {SUPABASE_CONFIG.ANON_KEY ? '✅ Set' : '❌ Not Set'}</Text>
      </View>

      <View style={{ backgroundColor: 'white', padding: 15, marginBottom: 10, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
          OAuth Configuration:
        </Text>
        <Text>Google iOS: {OAUTH_CONFIG.GOOGLE.CLIENT_ID_IOS || 'Not Set'}</Text>
        <Text>Google Android: {OAUTH_CONFIG.GOOGLE.CLIENT_ID_ANDROID || 'Not Set'}</Text>
        <Text>Google Web: {OAUTH_CONFIG.GOOGLE.CLIENT_ID_WEB || 'Not Set'}</Text>
        <Text>Apple: {OAUTH_CONFIG.APPLE.CLIENT_ID || 'Not Set'}</Text>
        <Text>Microsoft: {OAUTH_CONFIG.MICROSOFT.CLIENT_ID || 'Not Set'}</Text>
      </View>

      <View style={{ backgroundColor: 'white', padding: 15, marginBottom: 10, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
          Raw Values Check:
        </Text>
        <Text>extra.ENABLE_GOOGLE_SIGNIN: "{extra.ENABLE_GOOGLE_SIGNIN}"</Text>
        <Text>Type: {typeof extra.ENABLE_GOOGLE_SIGNIN}</Text>
        <Text>=== 'true': {extra.ENABLE_GOOGLE_SIGNIN === 'true' ? 'YES' : 'NO'}</Text>
        <Text>== true: {extra.ENABLE_GOOGLE_SIGNIN == true ? 'YES' : 'NO'}</Text>
      </View>
    </ScrollView>
  );
};

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import { useAuth } from '@/context/AuthContext';
import { apiClient } from '@/services/apiClient';

interface ContentInsights {
  total_items: number;
  insights: string;
  common_themes: string[];
  sentiment_distribution: Record<string, number>;
  tag_frequency: Record<string, number>;
}

interface ContentInsightsProps {
  contentType?: string;
  refreshTrigger?: number;
}

const screenWidth = Dimensions.get('window').width;

export const ContentInsights: React.FC<ContentInsightsProps> = ({
  contentType,
  refreshTrigger,
}) => {
  const [insights, setInsights] = useState<ContentInsights | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      fetchInsights();
    }
  }, [user, contentType, refreshTrigger]);

  const fetchInsights = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const params = contentType ? { content_type: contentType } : {};
      const response = await apiClient.get<ContentInsights>('/search/insights', { params });
      if (response.data) {
        setInsights(response.data);
      }
    } catch (err) {
      console.error('Error fetching insights:', err);
      setError('Failed to load insights');
    } finally {
      setLoading(false);
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return '#28a745';
      case 'negative':
        return '#dc3545';
      case 'neutral':
        return '#6c757d';
      default:
        return '#007bff';
    }
  };

  const prepareSentimentChartData = () => {
    if (!insights?.sentiment_distribution) return [];
    
    return Object.entries(insights.sentiment_distribution).map(([sentiment, count]) => ({
      name: sentiment.charAt(0).toUpperCase() + sentiment.slice(1),
      population: count,
      color: getSentimentColor(sentiment),
      legendFontColor: '#333',
      legendFontSize: 12,
    }));
  };

  const prepareTagChartData = () => {
    if (!insights?.tag_frequency) return { labels: [], datasets: [{ data: [] }] };
    
    const topTags = Object.entries(insights.tag_frequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 8);
    
    return {
      labels: topTags.map(([tag]) => tag.length > 8 ? `${tag.substring(0, 8)}...` : tag),
      datasets: [{
        data: topTags.map(([, count]) => count),
      }],
    };
  };

  const renderStatCard = (title: string, value: string | number, icon: string, color: string) => (
    <View style={[styles.statCard, { borderLeftColor: color }]}>
      <View style={styles.statHeader}>
        <Ionicons name={icon as any} size={24} color={color} />
        <Text style={styles.statTitle}>{title}</Text>
      </View>
      <Text style={[styles.statValue, { color }]}>{value}</Text>
    </View>
  );

  const renderThemesList = () => {
    if (!insights?.common_themes || insights.common_themes.length === 0) {
      return (
        <Text style={styles.noDataText}>No common themes identified yet.</Text>
      );
    }

    return (
      <View style={styles.themesList}>
        {insights.common_themes.slice(0, 6).map((theme, index) => (
          <View key={index} style={styles.themeItem}>
            <View style={styles.themeBullet} />
            <Text style={styles.themeText}>{theme}</Text>
          </View>
        ))}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007bff" />
        <Text style={styles.loadingText}>Analyzing your content...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle-outline" size={48} color="#dc3545" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchInsights}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  if (!insights || insights.total_items === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="analytics-outline" size={48} color="#ccc" />
        <Text style={styles.emptyText}>No content to analyze</Text>
        <Text style={styles.emptySubtext}>
          Upload some images, ask questions, or add content to see insights.
        </Text>
      </View>
    );
  }

  const sentimentData = prepareSentimentChartData();
  const tagData = prepareTagChartData();

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Overview Stats */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Overview</Text>
        <View style={styles.statsGrid}>
          {renderStatCard('Total Items', insights.total_items, 'library-outline', '#007bff')}
          {renderStatCard(
            'Themes Found',
            insights.common_themes.length,
            'bulb-outline',
            '#ffc107'
          )}
        </View>
      </View>

      {/* AI Insights */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>AI Insights</Text>
        <View style={styles.insightsCard}>
          <Ionicons name="sparkles-outline" size={20} color="#007bff" style={styles.insightsIcon} />
          <Text style={styles.insightsText}>{insights.insights}</Text>
        </View>
      </View>

      {/* Common Themes */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Common Themes</Text>
        {renderThemesList()}
      </View>

      {/* Sentiment Distribution */}
      {sentimentData.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sentiment Distribution</Text>
          <View style={styles.chartContainer}>
            <PieChart
              data={sentimentData}
              width={screenWidth - 32}
              height={200}
              chartConfig={{
                color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
              }}
              accessor="population"
              backgroundColor="transparent"
              paddingLeft="15"
              absolute
            />
          </View>
        </View>
      )}

      {/* Top Tags */}
      {tagData.labels.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Most Common Tags</Text>
          <View style={styles.chartContainer}>
            <BarChart
              data={tagData}
              width={screenWidth - 32}
              height={220}
              yAxisLabel=""
              yAxisSuffix=""
              chartConfig={{
                backgroundColor: '#ffffff',
                backgroundGradientFrom: '#ffffff',
                backgroundGradientTo: '#ffffff',
                decimalPlaces: 0,
                color: (opacity = 1) => `rgba(0, 123, 255, ${opacity})`,
                labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                style: {
                  borderRadius: 16,
                },
                propsForLabels: {
                  fontSize: 10,
                },
              }}
              style={styles.chart}
              showValuesOnTopOfBars
              fromZero
            />
          </View>
        </View>
      )}

      <View style={styles.bottomPadding} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 40,
  },
  errorText: {
    fontSize: 16,
    color: '#dc3545',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  retryButton: {
    backgroundColor: '#007bff',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    lineHeight: 20,
  },
  section: {
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 4,
    borderLeftWidth: 4,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statTitle: {
    fontSize: 12,
    color: '#666',
    marginLeft: 8,
    fontWeight: '600',
  },
  statValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  insightsCard: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  insightsIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  insightsText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  themesList: {
    marginTop: 8,
  },
  themeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  themeBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#007bff',
    marginRight: 12,
  },
  themeText: {
    flex: 1,
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
  chartContainer: {
    alignItems: 'center',
    marginTop: 8,
  },
  chart: {
    borderRadius: 16,
  },
  noDataText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  bottomPadding: {
    height: 20,
  },
});

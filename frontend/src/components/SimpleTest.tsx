import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export function SimpleTest() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>WhereZ App</Text>
      <Text style={styles.subtitle}>Web Platform Test</Text>
      <Text style={styles.message}>
        If you can see this message, the app is working correctly!
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 18,
    color: '#8E8E93',
    marginBottom: 20,
  },
  message: {
    fontSize: 16,
    color: '#000000',
    textAlign: 'center',
    lineHeight: 24,
  },
});

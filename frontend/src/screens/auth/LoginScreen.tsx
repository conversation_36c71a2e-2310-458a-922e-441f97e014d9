import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  Image,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Toast from 'react-native-toast-message';

import { useAuth } from '@/context/AuthContext';
import { authService } from '@/services/auth';
import { Button } from '@/components/Button';
import { COLORS, SPACING, TYPOGRAPHY, OAUTH_COLORS } from '@/constants';

export function LoginScreen() {
  const { signInWithApple, signInWithGoogle, signInWithMicrosoft, isLoading, error, clearError } = useAuth();
  const [availableProviders, setAvailableProviders] = useState({
    apple: false,
    google: false,
    microsoft: false,
  });

  useEffect(() => {
    checkAvailableProviders();
  }, []);

  useEffect(() => {
    if (error) {
      Toast.show({
        type: 'error',
        text1: 'Sign In Failed',
        text2: error,
      });
      clearError();
    }
  }, [error, clearError]);

  const checkAvailableProviders = async () => {
    const [apple, google, microsoft] = await Promise.all([
      authService.isAppleSignInAvailable(),
      authService.isGoogleSignInAvailable(),
      authService.isMicrosoftSignInAvailable(),
    ]);

    setAvailableProviders({ apple, google, microsoft });
  };

  const handleAppleSignIn = async () => {
    try {
      await signInWithApple();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Apple Sign In Failed',
        text2: error.message,
      });
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signInWithGoogle();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Google Sign In Failed',
        text2: error.message,
      });
    }
  };

  const handleMicrosoftSignIn = async () => {
    try {
      await signInWithMicrosoft();
    } catch (error: any) {
      Toast.show({
        type: 'error',
        text1: 'Microsoft Sign In Failed',
        text2: error.message,
      });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.secondary]}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Logo and Title */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Ionicons name="help-circle" size={80} color={COLORS.white} />
            </View>
            <Text style={styles.title}>WhereZ</Text>
            <Text style={styles.subtitle}>
              Ask questions, get answers with images and audio
            </Text>
          </View>

          {/* Sign In Options */}
          <View style={styles.signInContainer}>
            <Text style={styles.signInTitle}>Sign in to continue</Text>

            {availableProviders.apple && (
              <Button
                title="Continue with Apple"
                onPress={handleAppleSignIn}
                variant="outline"
                size="large"
                loading={isLoading}
                style={{ ...styles.signInButton, backgroundColor: OAUTH_COLORS.apple }}
                textStyle={{ color: COLORS.white }}
              />
            )}

            {availableProviders.google && (
              <Button
                title="Continue with Google"
                onPress={handleGoogleSignIn}
                variant="outline"
                size="large"
                loading={isLoading}
                style={{ ...styles.signInButton, backgroundColor: OAUTH_COLORS.google }}
                textStyle={{ color: COLORS.white }}
              />
            )}

            {availableProviders.microsoft && (
              <Button
                title="Continue with Microsoft"
                onPress={handleMicrosoftSignIn}
                variant="outline"
                size="large"
                loading={isLoading}
                style={{ ...styles.signInButton, backgroundColor: OAUTH_COLORS.microsoft }}
                textStyle={{ color: COLORS.white }}
              />
            )}

            {!availableProviders.apple && !availableProviders.google && !availableProviders.microsoft && (
              <View style={styles.noProvidersContainer}>
                <Text style={styles.noProvidersText}>
                  No sign-in providers are currently available.
                </Text>
                <Text style={styles.noProvidersSubtext}>
                  Please check your configuration and try again.
                </Text>
              </View>
            )}
          </View>



          {/* Footer */}
          <View style={styles.footer}>
            <Text style={styles.footerText}>
              By signing in, you agree to our Terms of Service and Privacy Policy
            </Text>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  
  gradient: {
    flex: 1,
  },
  
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
    justifyContent: 'space-between',
  },
  
  header: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: SPACING.xxxl,
  },
  
  logoContainer: {
    marginBottom: SPACING.lg,
  },
  
  title: {
    fontSize: TYPOGRAPHY.sizes.xxxl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.white,
    marginBottom: SPACING.sm,
  },
  
  subtitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    color: COLORS.white,
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: TYPOGRAPHY.lineHeights.relaxed * TYPOGRAPHY.sizes.lg,
  },
  
  signInContainer: {
    paddingBottom: SPACING.xl,
  },
  
  signInTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  
  signInButton: {
    marginBottom: SPACING.md,
    borderColor: COLORS.white,
    borderWidth: 1,
  },
  
  noProvidersContainer: {
    padding: SPACING.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    alignItems: 'center',
  },
  
  noProvidersText: {
    fontSize: TYPOGRAPHY.sizes.md,
    fontWeight: TYPOGRAPHY.weights.medium,
    color: COLORS.white,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  
  noProvidersSubtext: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.white,
    textAlign: 'center',
    opacity: 0.8,
  },
  
  footer: {
    paddingBottom: SPACING.lg,
  },
  
  footerText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.white,
    textAlign: 'center',
    opacity: 0.8,
    lineHeight: TYPOGRAPHY.lineHeights.relaxed * TYPOGRAPHY.sizes.sm,
  },
});

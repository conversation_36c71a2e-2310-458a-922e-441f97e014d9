# WhereZ Mobile App

React Native mobile application for the WhereZ MVP, built with Expo, TypeScript, and Supabase integration.

## Features

- **Supabase Integration**: Full backend-as-a-service integration with real-time updates
- **OAuth Authentication**: Apple Sign-In, Google Sign-In, Microsoft Sign-In via Supabase Auth
- **Question & Answer System**: Text and audio input with image context
- **Image Upload**: Camera and gallery integration with Supabase Storage
- **User Profile Management**: View and edit user information
- **Real-time Updates**: Live question status updates and notifications
- **Cross-Platform**: iOS and Android support with Expo

## Technology Stack

- **Framework**: React Native with Expo SDK 49
- **Language**: TypeScript
- **Backend**: Supabase (PostgreSQL + Auth + Storage + Real-time)
- **Navigation**: React Navigation 6
- **State Management**: React Context + Hooks
- **HTTP Client**: Axios + Supabase Client
- **Authentication**: Expo Auth Session + Supabase Auth + OAuth providers
- **Storage**: Supabase Storage + Expo SecureStore for sensitive data
- **UI Components**: Custom components with consistent design system
- **Real-time**: Supabase real-time subscriptions

## Quick Start

### Prerequisites

- Node.js 18+ and npm/yarn
- Expo CLI: `npm install -g @expo/cli`
- iOS Simulator (for iOS development)
- Android Studio/Emulator (for Android development)
- Expo Go app on physical device (optional)

### Installation

1. **Navigate to frontend directory**:
   ```bash
   cd frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the development server**:
   ```bash
   npm start
   ```

5. **Run on specific platform**:
   ```bash
   npm run ios     # iOS Simulator
   npm run android # Android Emulator
   npm run web     # Web browser
   ```

## Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Supabase Configuration (Recommended)
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key
USE_SUPABASE=true

# API Configuration (Optional - for hybrid mode)
API_BASE_URL=http://localhost:8000
API_TIMEOUT=30000

# OAuth Providers (configured in Supabase Dashboard)
GOOGLE_CLIENT_ID_IOS=your-google-client-id-ios
GOOGLE_CLIENT_ID_ANDROID=your-google-client-id-android
APPLE_CLIENT_ID=com.wheresz.mobile
MICROSOFT_CLIENT_ID=your-microsoft-client-id

# Feature Flags
ENABLE_APPLE_SIGNIN=true
ENABLE_GOOGLE_SIGNIN=true
ENABLE_MICROSOFT_SIGNIN=true
```

### OAuth Provider Setup

#### Google Sign-In
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create OAuth 2.0 credentials for iOS and Android
3. Add bundle IDs and package names
4. Download configuration files if needed

#### Apple Sign-In
1. Enable Sign In with Apple in Apple Developer Portal
2. Configure App ID with Sign In with Apple capability
3. Set up bundle identifier in app.json

#### Microsoft Sign-In
1. Register app in Azure Portal
2. Configure redirect URIs
3. Set up authentication platform for mobile

## Project Structure

```
frontend/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── Button.tsx
│   │   ├── LoadingScreen.tsx
│   │   └── ...
│   ├── context/            # React Context providers
│   │   └── AuthContext.tsx
│   ├── navigation/         # Navigation configuration
│   │   ├── AppNavigator.tsx
│   │   ├── AuthNavigator.tsx
│   │   └── ...
│   ├── screens/           # Screen components
│   │   ├── auth/
│   │   ├── home/
│   │   ├── questions/
│   │   ├── images/
│   │   └── profile/
│   ├── services/          # API and external services
│   │   ├── api.ts
│   │   └── auth.ts
│   ├── types/             # TypeScript type definitions
│   │   └── index.ts
│   ├── constants/         # App constants and configuration
│   │   └── index.ts
│   └── utils/             # Utility functions
│       └── toastConfig.tsx
├── assets/                # Images, fonts, etc.
├── App.tsx               # Root component
├── app.json              # Expo configuration
├── package.json          # Dependencies and scripts
└── tsconfig.json         # TypeScript configuration
```

## Key Features Implementation

### Authentication Flow

1. **OAuth Integration**: Uses Expo Auth Session for web-based OAuth flows
2. **Token Management**: Secure storage with automatic refresh
3. **Provider Support**: Apple, Google, Microsoft Sign-In
4. **State Management**: React Context for global auth state

### API Integration

- **Axios Client**: Configured with interceptors for auth and error handling
- **Token Refresh**: Automatic token refresh on 401 responses
- **Error Handling**: Centralized error handling with user-friendly messages
- **Request/Response Types**: Full TypeScript support

### Navigation Structure

- **Stack Navigation**: For hierarchical navigation within features
- **Tab Navigation**: Bottom tabs for main app sections
- **Authentication Flow**: Conditional navigation based on auth state
- **Deep Linking**: Support for URL-based navigation

### UI/UX Design

- **Design System**: Consistent colors, typography, and spacing
- **Component Library**: Reusable components with variants
- **Responsive Design**: Adapts to different screen sizes
- **Loading States**: Proper loading indicators and error states

## Development

### Available Scripts

```bash
# Development
npm start          # Start Expo development server
npm run ios        # Run on iOS simulator
npm run android    # Run on Android emulator
npm run web        # Run in web browser

# Code Quality
npm run lint       # Run ESLint
npm run lint:fix   # Fix ESLint issues
npm run type-check # Run TypeScript compiler

# Testing
npm test           # Run Jest tests
npm run test:watch # Run tests in watch mode
npm run test:coverage # Generate coverage report
```

### Code Style

- **ESLint**: Configured with React Native and TypeScript rules
- **TypeScript**: Strict mode enabled with path mapping
- **Prettier**: Code formatting (configure in your editor)
- **Import Organization**: Absolute imports with path aliases

### State Management

- **Authentication**: React Context for user state and auth actions
- **Local State**: useState and useReducer for component state
- **API State**: Custom hooks for data fetching and caching
- **Form State**: Controlled components with validation

## Building and Deployment

### Development Build

```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Configure EAS
eas build:configure

# Build for development
eas build --platform ios --profile development
eas build --platform android --profile development
```

### Production Build

```bash
# Build for app stores
eas build --platform ios --profile production
eas build --platform android --profile production

# Submit to app stores
eas submit --platform ios
eas submit --platform android
```

### Environment-Specific Builds

Configure different environments in `eas.json`:

```json
{
  "build": {
    "development": {
      "env": {
        "API_BASE_URL": "http://localhost:8000"
      }
    },
    "staging": {
      "env": {
        "API_BASE_URL": "https://staging-api.wheresz.com"
      }
    },
    "production": {
      "env": {
        "API_BASE_URL": "https://api.wheresz.com"
      }
    }
  }
}
```

## Testing

### Unit Testing

```bash
# Run all tests
npm test

# Run specific test file
npm test -- Button.test.tsx

# Run tests with coverage
npm run test:coverage
```

### Test Structure

```
__tests__/
├── components/
│   ├── Button.test.tsx
│   └── LoadingScreen.test.tsx
├── services/
│   ├── api.test.ts
│   └── auth.test.ts
├── context/
│   └── AuthContext.test.tsx
└── utils/
    └── helpers.test.ts
```

## Troubleshooting

### Common Issues

1. **Metro bundler issues**: Clear cache with `npx expo start --clear`
2. **iOS build failures**: Check Xcode version and iOS deployment target
3. **Android build issues**: Verify Android SDK and build tools
4. **OAuth not working**: Check redirect URIs and client IDs

### Debug Mode

```bash
# Enable debug mode
export DEBUG=1

# Run with debug logging
npx expo start --dev-client
```

### Performance Optimization

- **Image Optimization**: Use optimized image formats and sizes
- **Bundle Analysis**: Use `npx expo export` to analyze bundle size
- **Memory Management**: Proper cleanup of listeners and timers
- **Navigation**: Use lazy loading for screens

## Contributing

1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Follow TypeScript and ESLint rules
4. Add tests for new functionality
5. Update documentation as needed
6. Submit pull request


import 'dotenv/config';

export default {
  expo: {
    name: process.env.APP_NAME || "WhereZ",
    slug: "wheresz-mobile",
    version: process.env.APP_VERSION || "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.wheresz.mobile",
      infoPlist: {
        NSCameraUsageDescription: "This app needs access to camera to take photos for questions.",
        NSMicrophoneUsageDescription: "This app needs access to microphone to record audio questions.",
        NSPhotoLibraryUsageDescription: "This app needs access to photo library to select images for questions."
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#FFFFFF"
      },
      package: "com.wheresz.mobile",
      permissions: [
        "android.permission.CAMERA",
        "android.permission.RECORD_AUDIO",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE"
      ]
    },
    web: {
      favicon: "./assets/favicon.png",
      bundler: "metro"
    },
    plugins: [
      "expo-apple-authentication",
      [
        "expo-camera",
        {
          cameraPermission: "Allow WhereZ to access your camera to take photos for questions."
        }
      ],
      [
        "expo-image-picker",
        {
          photosPermission: "Allow WhereZ to access your photos to select images for questions.",
          cameraPermission: "Allow WhereZ to access your camera to take photos for questions."
        }
      ],
      [
        "expo-av",
        {
          microphonePermission: "Allow WhereZ to access your microphone to record audio questions."
        }
      ]
    ],
    scheme: "wheresz",
    extra: {
      eas: {
        projectId: "your-eas-project-id"
      },
      // API Configuration
      API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:8000',
      API_TIMEOUT: process.env.API_TIMEOUT || '30000',
      
      // Supabase Configuration
      SUPABASE_URL: process.env.SUPABASE_URL || '',
      SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY || '',
      USE_SUPABASE: process.env.USE_SUPABASE || 'false',
      
      // OAuth Configuration
      GOOGLE_CLIENT_ID_IOS: process.env.GOOGLE_CLIENT_ID_IOS || '',
      GOOGLE_CLIENT_ID_ANDROID: process.env.GOOGLE_CLIENT_ID_ANDROID || '',
      GOOGLE_CLIENT_ID_WEB: process.env.GOOGLE_CLIENT_ID_WEB || '',
      APPLE_CLIENT_ID: process.env.APPLE_CLIENT_ID || 'com.wheresz.mobile',
      MICROSOFT_CLIENT_ID: process.env.MICROSOFT_CLIENT_ID || '',
      MICROSOFT_REDIRECT_URI: process.env.MICROSOFT_REDIRECT_URI || 'wheresz://auth',
      FACEBOOK_CLIENT_ID: process.env.FACEBOOK_CLIENT_ID || '',
      
      // App Configuration
      APP_NAME: process.env.APP_NAME || 'WhereZ',
      APP_VERSION: process.env.APP_VERSION || '1.0.0',
      DEBUG: process.env.DEBUG || 'false',
      
      // Feature Flags
      ENABLE_APPLE_SIGNIN: process.env.ENABLE_APPLE_SIGNIN || 'true',
      ENABLE_GOOGLE_SIGNIN: process.env.ENABLE_GOOGLE_SIGNIN || 'true',
      ENABLE_MICROSOFT_SIGNIN: process.env.ENABLE_MICROSOFT_SIGNIN || 'true',
      ENABLE_FACEBOOK_SIGNIN: process.env.ENABLE_FACEBOOK_SIGNIN || 'true',
      ENABLE_AUDIO_QUESTIONS: process.env.ENABLE_AUDIO_QUESTIONS || 'true',
      ENABLE_IMAGE_CONTEXT: process.env.ENABLE_IMAGE_CONTEXT || 'true',
      
      // Upload Configuration
      MAX_IMAGE_SIZE: process.env.MAX_IMAGE_SIZE || '10485760',
      MAX_AUDIO_DURATION: process.env.MAX_AUDIO_DURATION || '300',
      SUPPORTED_IMAGE_FORMATS: process.env.SUPPORTED_IMAGE_FORMATS || 'jpg,jpeg,png,gif,webp',
      SUPPORTED_AUDIO_FORMATS: process.env.SUPPORTED_AUDIO_FORMATS || 'mp3,wav,m4a,aac',
      
      // Cache Configuration
      CACHE_IMAGES: process.env.CACHE_IMAGES || 'false',
      CACHE_DURATION: process.env.CACHE_DURATION || '86400000'
    }
  }
};

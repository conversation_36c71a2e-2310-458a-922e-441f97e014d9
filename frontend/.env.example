# API Configuration
API_BASE_URL=http://localhost:8000
API_TIMEOUT=30000

# Supabase Configuration
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-anon-key
USE_SUPABASE=true

# OAuth Configuration
# Google Sign-In
GOOGLE_CLIENT_ID_IOS=your-google-client-id-ios.googleusercontent.com
GOOGLE_CLIENT_ID_ANDROID=your-google-client-id-android.googleusercontent.com
GOOGLE_CLIENT_ID_WEB=your-google-client-id-web.googleusercontent.com

# Apple Sign-In (iOS only)
APPLE_CLIENT_ID=com.wheresz.mobile

# Microsoft Sign-In
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_REDIRECT_URI=wheresz://auth

# Facebook Sign-In
FACEBOOK_CLIENT_ID=your-facebook-client-id

# App Configuration
APP_NAME=WhereZ
APP_VERSION=1.0.0
DEBUG=true

# Feature Flags
ENABLE_APPLE_SIGNIN=true
ENABLE_GOOGLE_SIGNIN=true
ENABLE_MICROSOFT_SIGNIN=true
ENABLE_AUDIO_QUESTIONS=true
ENABLE_IMAGE_CONTEXT=true

# Upload Configuration
MAX_IMAGE_SIZE=10485760  # 10MB in bytes
MAX_AUDIO_DURATION=300   # 5 minutes in seconds
SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,gif,webp
SUPPORTED_AUDIO_FORMATS=mp3,wav,m4a,aac

# Cache Configuration
CACHE_IMAGES=true
CACHE_DURATION=86400000  # 24 hours in milliseconds

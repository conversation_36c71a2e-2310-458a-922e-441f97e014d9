<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication - WhereZ</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007AFF;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Completing sign in...</h2>
        <p>Please wait while we complete your authentication.</p>
    </div>

    <script>
        // This page handles OAuth callbacks and redirects back to the main app
        (function() {
            try {
                // Check if we're in an iframe (popup flow)
                if (window.opener) {
                    // Send message to parent window
                    window.opener.postMessage({
                        type: 'oauth_callback',
                        url: window.location.href
                    }, window.location.origin);
                    window.close();
                } else {
                    // Direct navigation - redirect to main app
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 2000);
                }
            } catch (error) {
                console.error('OAuth callback error:', error);
                // Fallback redirect
                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
            }
        })();
    </script>
</body>
</html>

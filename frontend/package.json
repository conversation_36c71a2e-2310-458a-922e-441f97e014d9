{"name": "wheresz-mobile", "version": "1.0.0", "description": "WhereZ Mobile App - React Native with Expo", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-google-signin/google-signin": "^10.1.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@supabase/supabase-js": "^2.38.5", "@testing-library/react-native": "^13.2.0", "axios": "^1.6.2", "dotenv": "^17.0.0", "expo": "^53.0.13", "expo-apple-authentication": "~7.2.4", "expo-auth-session": "~6.2.0", "expo-av": "~15.1.6", "expo-camera": "~16.1.9", "expo-clipboard": "^7.1.4", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-document-picker": "~13.1.6", "expo-font": "~13.3.1", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-media-library": "~17.1.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-toast-message": "^2.1.7", "react-native-url-polyfill": "^2.0.0", "react-native-uuid": "^2.0.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.8", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.54.0", "eslint-config-expo": "~9.2.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "jest-expo": "~53.0.7", "typescript": "~5.8.3"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0", "@types/react": "~19.0.10"}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"], "collectCoverageFrom": ["src/**/*.{ts,tsx}", "!src/**/*.d.ts", "!src/**/__tests__/**", "!src/**/index.ts"]}, "private": true, "keywords": ["react-native", "expo", "typescript", "mobile-app", "o<PERSON>h", "image-upload", "question-answer"], "author": "WhereZ Team"}
credentials/


# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Expo
.expo/
dist/
web-build/

# React Native
package-lock.json
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Logs
logs
*.log

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Images and uploads (for development)
uploads/
images/
media/

# Backup files
*.bak
*.backup

# Local configuration
config.local.json
settings.local.json

# Supabase
.temp/
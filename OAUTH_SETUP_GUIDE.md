# Complete OAuth Setup Guide

This comprehensive guide explains how to set up OAuth authentication for Google, Apple, Microsoft, and Facebook using Supabase for both frontend and backend authentication.

## Overview

The OAuth implementation uses:
- **Supabase OAuth** for web platform (seamless redirect flow)
- **Native SDKs** for mobile platforms (iOS/Android)
- **Unified authentication context** that handles all providers consistently
- **Automatic session management** with real-time auth state updates

## Prerequisites

1. **Supabase Project**: Create a project at [supabase.com](https://supabase.com)
2. **OAuth Provider Accounts**: Set up developer accounts for each provider
3. **Development Environment**: Node.js, Expo CLI, and your preferred code editor

## Provider Setup

### 1. Google OAuth Setup

#### Google Cloud Console Configuration

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the **Google+ API** and **Google OAuth2 API**
4. Go to **Credentials** → **Create Credentials** → **OAuth 2.0 Client IDs**

Create separate OAuth clients for different platforms:

**Web Application Client:**
- **Application type**: Web application
- **Name**: WhereZ Web Client
- **Authorized JavaScript origins**: 
  ```
  http://localhost:8083
  http://localhost:8081
  http://localhost:8082
  https://your-production-domain.com
  ```
- **Authorized redirect URIs**:
  ```
  http://localhost:8083/auth/callback
  http://localhost:8081/auth/callback
  http://localhost:8082/auth/callback
  https://your-production-domain.com/auth/callback
  ```

**iOS Application Client:**
- **Application type**: iOS
- **Name**: WhereZ iOS Client
- **Bundle ID**: `com.wheresz.mobile`

**Android Application Client:**
- **Application type**: Android
- **Name**: WhereZ Android Client
- **Package name**: `com.wheresz.mobile`
- **SHA-1 certificate fingerprint**: Get from your keystore

#### Environment Configuration

Update `frontend/.env`:
```env
GOOGLE_CLIENT_ID_WEB=your-web-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_ID_IOS=your-ios-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_ID_ANDROID=your-android-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-web-client-secret
```

Update `supabase/.env`:
```env
GOOGLE_CLIENT_ID=your-web-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-web-client-secret
```

### 2. Apple OAuth Setup

#### Apple Developer Configuration

1. Go to [Apple Developer Console](https://developer.apple.com/)
2. Navigate to **Certificates, Identifiers & Profiles**
3. Create a new **App ID** with Sign In with Apple capability
4. Create a **Services ID** for web authentication:
   - **Description**: WhereZ Web Service
   - **Identifier**: `com.wheresz.web`
   - **Configure Sign In with Apple**:
     - **Primary App ID**: Select your app ID
     - **Web Domain**: `your-domain.com`
     - **Return URLs**: `https://your-supabase-url.supabase.co/auth/v1/callback`

#### Environment Configuration

Update `frontend/.env`:
```env
APPLE_CLIENT_ID=com.wheresz.mobile
```

Update `supabase/.env`:
```env
APPLE_CLIENT_ID=com.wheresz.web
APPLE_SECRET=your-apple-secret-key
```

### 3. Microsoft OAuth Setup

#### Azure AD Configuration

1. Go to [Azure Portal](https://portal.azure.com/)
2. Navigate to **Azure Active Directory** → **App registrations**
3. Click **New registration**:
   - **Name**: WhereZ
   - **Supported account types**: Accounts in any organizational directory and personal Microsoft accounts
   - **Redirect URI**: 
     - **Platform**: Web
     - **URI**: `https://your-supabase-url.supabase.co/auth/v1/callback`

4. After creation, note the **Application (client) ID**
5. Go to **Certificates & secrets** → **New client secret**
6. Go to **API permissions** → **Add a permission** → **Microsoft Graph**:
   - Add `openid`, `profile`, `email` permissions

#### Environment Configuration

Update `frontend/.env`:
```env
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_REDIRECT_URI=wheresz://auth
```

Update `supabase/.env`:
```env
MICROSOFT_CLIENT_ID=your-microsoft-client-id
MICROSOFT_CLIENT_SECRET=your-microsoft-client-secret
```

### 4. Facebook OAuth Setup

#### Facebook Developer Configuration

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or select existing one
3. Add **Facebook Login** product
4. Configure **Facebook Login** settings:
   - **Valid OAuth Redirect URIs**: 
     ```
     https://your-supabase-url.supabase.co/auth/v1/callback
     ```
   - **Valid OAuth Redirect URIs for Web**: 
     ```
     http://localhost:8083/auth/callback
     https://your-production-domain.com/auth/callback
     ```

5. Go to **Settings** → **Basic** to get your App ID and App Secret

#### Environment Configuration

Update `frontend/.env`:
```env
FACEBOOK_CLIENT_ID=your-facebook-app-id
```

Update `supabase/.env`:
```env
FACEBOOK_CLIENT_ID=your-facebook-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-app-secret
```

## Supabase Configuration

### 1. Update Supabase Dashboard

For production, configure OAuth providers in your Supabase dashboard:

1. Go to your Supabase project dashboard
2. Navigate to **Authentication** → **Providers**
3. Enable and configure each provider:

**Google:**
- **Client ID**: Your web client ID
- **Client Secret**: Your web client secret

**Apple:**
- **Client ID**: Your services ID
- **Client Secret**: Your Apple secret key

**Microsoft:**
- **Client ID**: Your Azure application ID
- **Client Secret**: Your Azure client secret

**Facebook:**
- **Client ID**: Your Facebook app ID
- **Client Secret**: Your Facebook app secret

### 2. Configure Redirect URLs

In your Supabase project settings, add these redirect URLs:
```
http://localhost:8083/auth/callback
http://localhost:8081/auth/callback
http://localhost:8082/auth/callback
https://your-production-domain.com/auth/callback
wheresz://auth/callback
```

## How the OAuth Flow Works

### Web Platform Flow

1. **User clicks OAuth button** → `signInWithGoogle()`, `signInWithApple()`, etc.
2. **Supabase OAuth redirect** → User redirected to provider's OAuth page
3. **User authorizes** → Provider redirects back to `/auth/callback`
4. **Session detection** → Supabase automatically creates session
5. **Auth state listener** → App detects session and logs user in
6. **User authenticated** → App shows authenticated state

### Mobile Platform Flow

1. **User clicks OAuth button** → Native SDK opens
2. **User authorizes** → Native SDK returns tokens
3. **Supabase ID token exchange** → Convert to Supabase session
4. **Session storage** → Store session locally
5. **User authenticated** → App shows authenticated state

## Testing the Setup

### 1. Start Development Servers

```bash
# Terminal 1: Start Supabase
cd supabase && npx supabase start

# Terminal 2: Start frontend
cd frontend && npx expo start --web
```

### 2. Test Each Provider

1. Open `http://localhost:8083` in your browser
2. Try signing in with each provider:
   - **Google**: Should redirect to Google OAuth
   - **Apple**: Should redirect to Apple OAuth (web only)
   - **Microsoft**: Should redirect to Microsoft OAuth
   - **Facebook**: Should redirect to Facebook OAuth

### 3. Verify Authentication

After successful OAuth:
- User should be redirected back to your app
- App should detect the session automatically
- User should see authenticated state
- Check browser dev tools for any errors

## Troubleshooting

### Common Issues

**"Invalid redirect URI"**
- Ensure redirect URIs match exactly in provider console
- Check for trailing slashes or protocol mismatches

**"Client secret missing"**
- Verify client secrets are set in Supabase environment
- Check that provider is enabled in Supabase dashboard

**"CORS errors"**
- Ensure JavaScript origins are configured in provider console
- Verify Supabase site URL is correct

**"Session not detected"**
- Check that `detectSessionInUrl: true` in Supabase config
- Verify auth state listener is properly set up
- Check browser console for JavaScript errors

### Debug Steps

1. **Check browser network tab** for failed requests
2. **Verify environment variables** are loaded correctly
3. **Test Supabase connection** in browser console
4. **Check provider console logs** for OAuth errors
5. **Verify redirect URLs** match exactly

## Production Deployment

### 1. Update Provider Configurations

Add your production domain to all OAuth provider configurations:
- Google Cloud Console
- Apple Developer Console  
- Azure AD
- Facebook Developer Console

### 2. Update Environment Variables

Set production values for:
- `SUPABASE_URL`
- `SUPABASE_ANON_KEY`
- All OAuth client IDs and secrets

### 3. Configure HTTPS

OAuth providers require HTTPS in production:
- Set up SSL certificate
- Update all redirect URLs to use `https://`
- Test OAuth flow on production domain

## Security Best Practices

1. **Never commit secrets** to version control
2. **Use environment variables** for all sensitive data
3. **Regularly rotate** OAuth credentials
4. **Monitor OAuth usage** in provider consoles
5. **Implement proper session management**
6. **Use HTTPS** in production
7. **Validate tokens** on the backend
8. **Implement proper logout** functionality

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review provider documentation
3. Check Supabase documentation
4. Verify all configuration steps were completed
5. Test with a fresh browser session

For provider-specific issues, consult:
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Apple Sign In Documentation](https://developer.apple.com/sign-in-with-apple/)
- [Microsoft OAuth Documentation](https://docs.microsoft.com/en-us/azure/active-directory/develop/)
- [Facebook Login Documentation](https://developers.facebook.com/docs/facebook-login/)
- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)

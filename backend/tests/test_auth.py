"""Authentication tests."""

import pytest



def test_health_check():
    """Test health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"


def test_apple_signin_invalid_token():
    """Test Apple Sign-In with invalid token."""
    response = client.post(
        "/auth/oauth/apple", json={"identity_token": "invalid_token", "authorization_code": "invalid_code"}
    )
    assert response.status_code == 401


def test_google_signin_invalid_token():
    """Test Google Sign-In with invalid token."""
    response = client.post("/auth/oauth/google", json={"id_token": "invalid_token"})
    assert response.status_code == 401


def test_microsoft_signin_invalid_token():
    """Test Microsoft Sign-In with invalid token."""
    response = client.post("/auth/oauth/microsoft", json={"access_token": "invalid_token"})
    assert response.status_code == 401


def test_refresh_token_invalid():
    """Test refresh token with invalid token."""
    response = client.post("/auth/refresh", json={"refresh_token": "invalid_token"})
    assert response.status_code == 401


def test_logout_unauthorized():
    """Test logout without authentication."""
    response = client.post("/auth/logout")
    assert response.status_code == 401


# Add more comprehensive tests here for successful authentication flows
# These would require mocking the OAuth providers

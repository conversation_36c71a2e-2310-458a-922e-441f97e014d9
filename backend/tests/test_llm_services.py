"""Tests for LLM services and functionality."""

from unittest.mock import As<PERSON><PERSON><PERSON>, Magic<PERSON>ock, patch

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from app.models import ContentEmbedding, ContentType, Image, ProcessedContent, Question, User
from app.services.content_processor import ContentProcessor, content_processor
from app.services.embeddings_service import EmbeddingsService, embeddings_service
from app.services.intelligent_query_service import IntelligentQueryService, intelligent_query_service
from app.services.llm_service import LLMService, llm_service
from tests.conftest import create_test_image, create_test_question, create_test_user


class TestLLMService:
    """Test LLM service functionality."""

    @pytest.fixture
    def mock_openai_client(self):
        """Mock OpenAI client for testing."""
        with patch("app.services.llm_service.openai.AsyncOpenAI") as mock_client:
            yield mock_client

    @pytest.mark.asyncio
    async def test_analyze_image(self, mock_openai_client):
        """Test image analysis functionality."""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices[0].message.content = (
            '{"description": "A test image", "objects": ["object1"], "setting": "indoor", "text_content": "", "tags": ["test"]}'
        )

        mock_openai_client.return_value.chat.completions.create = AsyncMock(return_value=mock_response)

        llm = LLMService()
        result = await llm.analyze_image(b"fake_image_data")

        assert "description" in result
        assert "objects" in result
        assert "tags" in result
        assert result["description"] == "A test image"

    @pytest.mark.asyncio
    async def test_transcribe_audio(self, mock_openai_client):
        """Test audio transcription functionality."""
        # Mock OpenAI response
        mock_openai_client.return_value.audio.transcriptions.create = AsyncMock(
            return_value="Transcribed text"
        )

        llm = LLMService()
        result = await llm.transcribe_audio(b"fake_audio_data", "test.mp3")

        assert result == "Transcribed text"

    @pytest.mark.asyncio
    async def test_process_text(self, mock_openai_client):
        """Test text processing functionality."""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices[0].message.content = (
            '{"summary": "Test summary", "key_points": ["point1"], "tags": ["tag1"], "sentiment": "positive"}'
        )

        mock_openai_client.return_value.chat.completions.create = AsyncMock(return_value=mock_response)

        llm = LLMService()
        result = await llm.process_text("Test text to process")

        assert "summary" in result
        assert "key_points" in result
        assert "sentiment" in result

    @pytest.mark.asyncio
    async def test_generate_embeddings(self, mock_openai_client):
        """Test embedding generation."""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.data[0].embedding = [0.1, 0.2, 0.3] * 512  # 1536 dimensions

        mock_openai_client.return_value.embeddings.create = AsyncMock(return_value=mock_response)

        llm = LLMService()
        result = await llm.generate_embeddings("Test text")

        assert len(result) == 1536
        assert all(isinstance(x, float) for x in result)

    @pytest.mark.asyncio
    async def test_answer_question(self, mock_openai_client):
        """Test question answering functionality."""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices[0].message.content = "This is a test answer based on the provided context."

        mock_openai_client.return_value.chat.completions.create = AsyncMock(return_value=mock_response)

        llm = LLMService()
        result = await llm.answer_question(
            question="What is this about?", context=["Context information here"]
        )

        assert "answer" in result
        assert "confidence" in result
        assert "sources" in result
        assert result["answer"] == "This is a test answer based on the provided context."

    def test_count_tokens(self, client: TestClient):
        """Test token counting functionality."""
        llm = LLMService()
        token_count = llm.count_tokens("This is a test sentence.")

        assert isinstance(token_count, int)
        assert token_count > 0

    @pytest.mark.asyncio
    async def test_batch_process_texts(self, mock_openai_client):
        """Test batch text processing."""
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices[0].message.content = (
            '{"summary": "Test", "key_points": [], "tags": [], "sentiment": "neutral"}'
        )

        mock_openai_client.return_value.chat.completions.create = AsyncMock(return_value=mock_response)

        llm = LLMService()
        texts = ["Text 1", "Text 2", "Text 3"]
        results = await llm.batch_process_texts(texts)

        assert len(results) == 3
        assert all("summary" in result for result in results if "error" not in result)


class TestEmbeddingsService:
    """Test embeddings service functionality."""

    @pytest.fixture
    def user(self, db: Session):
        """Create a test user."""
        return create_test_user(db)

    @pytest.mark.asyncio
    async def test_create_embedding(self, db: Session, user: User):
        """Test embedding creation."""
        with patch("app.services.llm_service.llm_service.generate_embeddings") as mock_embeddings:
            mock_embeddings.return_value = [0.1] * 1536

            embedding = await embeddings_service.create_embedding(
                db=db,
                content_id=user.id,
                content_type=ContentType.TEXT,
                content_text="Test content",
                user_id=user.id,
            )

            assert embedding.content_text == "Test content"
            assert embedding.content_type == ContentType.TEXT
            assert embedding.user_id == user.id

    @pytest.mark.asyncio
    async def test_search_similar_content(self, db: Session, user: User):
        """Test semantic search functionality."""
        # Create test embeddings
        embedding1 = ContentEmbedding(
            content_id=user.id,
            content_type=ContentType.TEXT,
            content_text="Test content 1",
            embedding=[0.1] * 1536,
            user_id=user.id,
        )
        embedding2 = ContentEmbedding(
            content_id=user.id,
            content_type=ContentType.TEXT,
            content_text="Test content 2",
            embedding=[0.2] * 1536,
            user_id=user.id,
        )

        db.add_all([embedding1, embedding2])
        db.commit()

        with patch("app.services.llm_service.llm_service.generate_embeddings") as mock_embeddings:
            mock_embeddings.return_value = [0.15] * 1536

            with patch("sqlalchemy.orm.Session.execute") as mock_execute:
                # Mock database query result
                mock_result = MagicMock()
                mock_result.fetchall.return_value = [
                    MagicMock(
                        id=embedding1.id,
                        content_id=embedding1.content_id,
                        content_type=embedding1.content_type.value,
                        content_text=embedding1.content_text,
                        metadata={},
                        user_id=embedding1.user_id,
                        created_at=embedding1.created_at,
                        updated_at=embedding1.updated_at,
                        similarity=0.9,
                    )
                ]
                mock_execute.return_value = mock_result

                results = await embeddings_service.search_similar_content(
                    db=db, query_text="Test query", user_id=user.id
                )

                assert len(results) > 0
                embedding, similarity = results[0]
                assert embedding.content_text == "Test content 1"
                assert similarity == 0.9

    @pytest.mark.asyncio
    async def test_get_content_context(self, db: Session, user: User):
        """Test content context retrieval."""
        with patch.object(embeddings_service, "search_similar_content") as mock_search:
            mock_embedding = MagicMock()
            mock_embedding.content_text = "Relevant content"
            mock_embedding.metadata = {"key": "value"}

            mock_search.return_value = [(mock_embedding, 0.8)]

            context = await embeddings_service.get_content_context(
                db=db, query_text="Test query", user_id=user.id
            )

            assert len(context) > 0
            assert "Relevant content" in context[0]

    def test_delete_embeddings_by_content(self, db: Session, user: User):
        """Test embedding deletion."""
        embedding = ContentEmbedding(
            content_id=user.id, content_type=ContentType.TEXT, content_text="Test content", user_id=user.id
        )
        db.add(embedding)
        db.commit()

        deleted_count = embeddings_service.delete_embeddings_by_content(
            db=db, content_id=user.id, content_type=ContentType.TEXT
        )

        assert deleted_count == 1

    @pytest.mark.asyncio
    async def test_reindex_user_content(self, db: Session, user: User):
        """Test user content reindexing."""
        # Create processed content
        processed_content = ProcessedContent(
            content_id=user.id,
            content_type=ContentType.TEXT,
            processed_content="Processed text",
            user_id=user.id,
        )
        db.add(processed_content)
        db.commit()

        with patch("app.services.llm_service.llm_service.generate_embeddings") as mock_embeddings:
            mock_embeddings.return_value = [0.1] * 1536

            result = await embeddings_service.reindex_user_content(db=db, user_id=user.id)

            assert "reindexed_count" in result
            assert result["reindexed_count"] == 1


class TestIntelligentQueryService:
    """Test intelligent query service functionality."""

    @pytest.fixture
    def user(self, db: Session):
        """Create a test user."""
        return create_test_user(db)

    @pytest.fixture
    def question(self, db: Session, user: User):
        """Create a test question."""
        return create_test_question(db, user_id=user.id)

    @pytest.mark.asyncio
    async def test_answer_question_with_context(self, db: Session, user: User, question: Question):
        """Test context-aware question answering."""
        with patch.object(intelligent_query_service, "_get_relevant_context") as mock_context:
            mock_embedding = MagicMock()
            mock_embedding.content_text = "Relevant context"
            mock_embedding.content_type = ContentType.TEXT
            mock_context.return_value = [(mock_embedding, 0.8)]

            with patch("app.services.llm_service.llm_service.answer_question") as mock_llm:
                mock_llm.return_value = {
                    "answer": "Test answer",
                    "confidence": 0.9,
                    "sources": ["AI Analysis"],
                    "model": "gpt-4",
                }

                answer = await intelligent_query_service.answer_question_with_context(
                    db=db, question=question, user=user
                )

                assert answer.content == "Test answer"
                assert answer.confidence == 0.9
                assert answer.question_id == question.id

    @pytest.mark.asyncio
    async def test_semantic_search(self, db: Session, user: User):
        """Test semantic search functionality."""
        with patch.object(embeddings_service, "search_similar_content") as mock_search:
            mock_embedding = MagicMock()
            mock_search.return_value = [(mock_embedding, 0.8)]

            results = await intelligent_query_service.semantic_search(
                db=db, query="test query", user_id=user.id
            )

            assert len(results) > 0
            embedding, similarity = results[0]
            assert similarity == 0.8

    @pytest.mark.asyncio
    async def test_get_content_insights(self, db: Session, user: User):
        """Test content insights generation."""
        # Create processed content
        processed_content = ProcessedContent(
            content_id=user.id,
            content_type=ContentType.TEXT,
            summary="Test summary",
            tags=["tag1", "tag2"],
            sentiment="positive",
            user_id=user.id,
        )
        db.add(processed_content)
        db.commit()

        with patch("app.services.llm_service.llm_service.process_text") as mock_llm:
            mock_llm.return_value = {"summary": "Generated insights", "themes": ["theme1", "theme2"]}

            insights = await intelligent_query_service.get_content_insights(db=db, user_id=user.id)

            assert "total_items" in insights
            assert "insights" in insights
            assert "tag_frequency" in insights
            assert insights["total_items"] == 1

    @pytest.mark.asyncio
    async def test_suggest_related_content(self, db: Session, user: User):
        """Test related content suggestions."""
        # Create embedding
        embedding = ContentEmbedding(
            content_id=user.id, content_type=ContentType.TEXT, content_text="Test content", user_id=user.id
        )
        db.add(embedding)
        db.commit()

        with patch.object(embeddings_service, "search_similar_content") as mock_search:
            mock_related = MagicMock()
            mock_related.content_id = "different_id"
            mock_search.return_value = [(mock_related, 0.7)]

            related = await intelligent_query_service.suggest_related_content(
                db=db, content_id=user.id, content_type=ContentType.TEXT, user_id=user.id
            )

            assert len(related) > 0

    @pytest.mark.asyncio
    async def test_get_conversation_context(self, db: Session, user: User):
        """Test conversation context retrieval."""
        # Create questions with answers
        question = create_test_question(db, user_id=user.id)
        question.status = "completed"

        from app.models import Answer

        answer = Answer(content="Test answer", question_id=question.id)
        db.add(answer)
        db.commit()

        context = await intelligent_query_service.get_conversation_context(
            db=db, user_id=user.id, current_question="New question"
        )

        assert isinstance(context, list)


class TestContentProcessor:
    """Test content processor functionality."""

    @pytest.fixture
    def user(self, db: Session):
        """Create a test user."""
        return create_test_user(db)

    @pytest.fixture
    def image(self, db: Session, user: User):
        """Create a test image."""
        return create_test_image(db, user_id=user.id)

    @pytest.fixture
    def question(self, db: Session, user: User):
        """Create a test question."""
        return create_test_question(db, user_id=user.id)

    @pytest.mark.asyncio
    async def test_process_image(self, db: Session, user: User, image: Image):
        """Test image processing functionality."""
        with patch("app.services.content_processor.ContentProcessor._get_image_content") as mock_content:
            mock_content.return_value = b"fake_image_data"

            with patch("app.services.llm_service.llm_service.analyze_image") as mock_analyze:
                mock_analyze.return_value = {
                    "description": "Test image",
                    "objects": ["object1"],
                    "setting": "indoor",
                    "text_content": "",
                    "tags": ["test"],
                }

                with patch.object(embeddings_service, "create_embedding") as mock_embedding:
                    mock_embedding.return_value = MagicMock()

                    processed, embedding = await content_processor.process_image(
                        db=db, image=image, user=user
                    )

                    assert processed.content_type == ContentType.IMAGE
                    assert processed.user_id == user.id
                    assert image.llm_description == "Test image"

    @pytest.mark.asyncio
    async def test_process_text_question(self, db: Session, user: User, question: Question):
        """Test text question processing."""
        with patch("app.services.llm_service.llm_service.process_text") as mock_process:
            mock_process.return_value = {
                "summary": "Question summary",
                "key_points": ["point1"],
                "tags": ["tag1"],
                "sentiment": "neutral",
            }

            with patch.object(embeddings_service, "create_embedding") as mock_embedding:
                mock_embedding.return_value = MagicMock()

                processed, embedding = await content_processor.process_text_question(
                    db=db, question=question, user=user
                )

                assert processed.content_type == ContentType.TEXT
                assert processed.user_id == user.id
                assert question.llm_processed_content == question.content

    @pytest.mark.asyncio
    async def test_batch_process_user_content(self, db: Session, user: User):
        """Test batch content processing."""
        # Create test content
        image = create_test_image(db, user_id=user.id)
        question = create_test_question(db, user_id=user.id)

        with patch.object(content_processor, "process_image") as mock_process_image:
            with patch.object(content_processor, "process_text_question") as mock_process_text:
                mock_process_image.return_value = (MagicMock(), MagicMock())
                mock_process_text.return_value = (MagicMock(), MagicMock())

                results = await content_processor.batch_process_user_content(db=db, user_id=user.id)

                assert "images_processed" in results
                assert "questions_processed" in results
                assert "embeddings_created" in results

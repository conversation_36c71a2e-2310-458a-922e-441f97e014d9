"""Test database functionality without complex fixtures."""

import pytest
from pydantic import ValidationError

from app.models.field import FieldType
from app.schemas.database import DatabaseRequest, DatabaseUpdateRequest
from app.schemas.field import FieldRequest, FieldUpdateRequest
from app.schemas.record import RecordRequest, RecordFilterRequest


class TestDatabaseSchemas:
    """Test database-related schemas."""

    def test_database_request_validation(self):
        """Test database request validation."""
        # Valid request
        request = DatabaseRequest(
            name="Test Database",
            description="Test description",
            icon="📋",
            color="#3B82F6"
        )
        assert request.name == "Test Database"
        assert request.color == "#3B82F6"

        # Invalid color format
        with pytest.raises(ValidationError):
            DatabaseRequest(
                name="Test",
                color="invalid-color"
            )

        # Missing required name
        with pytest.raises(ValidationError):
            DatabaseRequest()

    def test_database_update_request(self):
        """Test database update request."""
        request = DatabaseUpdateRequest(
            name="Updated Name",
            color="#FF0000"
        )
        assert request.name == "Updated Name"
        assert request.color == "#FF0000"

        # All fields optional
        request = DatabaseUpdateRequest()
        assert request.name is None

    def test_field_request_validation(self):
        """Test field request validation."""
        # Valid text field
        request = FieldRequest(
            name="Text Field",
            type=FieldType.TEXT,
            settings={"required": True, "max_length": 100},
            position=0
        )
        assert request.name == "Text Field"
        assert request.type == FieldType.TEXT

        # Valid select field
        request = FieldRequest(
            name="Status",
            type=FieldType.SELECT,
            settings={"options": ["Draft", "Published"]},
            position=1
        )
        assert request.settings["options"] == ["Draft", "Published"]

        # Invalid select field - no options
        with pytest.raises(ValidationError):
            FieldRequest(
                name="Status",
                type=FieldType.SELECT,
                settings={"options": []},
                position=1
            )

    def test_field_update_request(self):
        """Test field update request."""
        request = FieldUpdateRequest(
            name="Updated Field",
            settings={"required": False},
            position=5
        )
        assert request.name == "Updated Field"
        assert request.position == 5

    def test_record_request_validation(self):
        """Test record request validation."""
        # Valid request
        request = RecordRequest(
            properties={
                "field_1": "value_1",
                "field_2": 42,
                "field_3": True
            }
        )
        assert request.properties["field_1"] == "value_1"
        assert request.properties["field_2"] == 42

        # Invalid properties type
        with pytest.raises(ValidationError):
            RecordRequest(properties="not a dict")

    def test_record_filter_request(self):
        """Test record filter request validation."""
        # Valid filter request
        request = RecordFilterRequest(
            filters=[
                {
                    "field_id": "field_1",
                    "operator": "equals",
                    "value": "test"
                }
            ],
            search="search term",
            sort_by="field_1",
            sort_order="desc",
            page=2,
            page_size=50
        )
        assert len(request.filters) == 1
        assert request.search == "search term"
        assert request.sort_order == "desc"

        # Invalid sort order
        with pytest.raises(ValidationError):
            RecordFilterRequest(sort_order="invalid")

        # Invalid operator
        with pytest.raises(ValidationError):
            RecordFilterRequest(
                filters=[
                    {
                        "field_id": "field_1",
                        "operator": "invalid_operator",
                        "value": "test"
                    }
                ]
            )

        # Missing required filter fields
        with pytest.raises(ValidationError):
            RecordFilterRequest(
                filters=[
                    {
                        "field_id": "field_1"
                        # Missing operator and value
                    }
                ]
            )


class TestFieldTypes:
    """Test field type enumeration."""

    def test_field_type_values(self):
        """Test field type enum values."""
        assert FieldType.TEXT.value == "text"
        assert FieldType.NUMBER.value == "number"
        assert FieldType.DATE.value == "date"
        assert FieldType.SELECT.value == "select"
        assert FieldType.MULTI_SELECT.value == "multi_select"
        assert FieldType.CHECKBOX.value == "checkbox"
        assert FieldType.URL.value == "url"
        assert FieldType.EMAIL.value == "email"
        assert FieldType.PHONE.value == "phone"
        assert FieldType.RICH_TEXT.value == "rich_text"

    def test_field_type_creation(self):
        """Test creating field types from strings."""
        assert FieldType("text") == FieldType.TEXT
        assert FieldType("select") == FieldType.SELECT
        
        # Invalid field type
        with pytest.raises(ValueError):
            FieldType("invalid_type")


class TestFieldValidation:
    """Test field validation logic."""

    def test_text_field_validation(self):
        """Test text field validation."""
        from app.models.field import Field
        
        field = Field(
            name="Test Field",
            type=FieldType.TEXT,
            settings={"required": True, "max_length": 10}
        )
        
        # Valid value
        is_valid, error = field.validate_value("test")
        assert is_valid
        assert error is None
        
        # Too long
        is_valid, error = field.validate_value("this is too long")
        assert not is_valid
        assert "maximum length" in error
        
        # Required field empty
        is_valid, error = field.validate_value("")
        assert not is_valid
        assert "required" in error

    def test_number_field_validation(self):
        """Test number field validation."""
        from app.models.field import Field
        
        field = Field(
            name="Number Field",
            type=FieldType.NUMBER,
            settings={"min_value": 0, "max_value": 100}
        )
        
        # Valid number
        is_valid, error = field.validate_value(50)
        assert is_valid
        
        # Too small
        is_valid, error = field.validate_value(-1)
        assert not is_valid
        assert "at least" in error
        
        # Too large
        is_valid, error = field.validate_value(101)
        assert not is_valid
        assert "at most" in error
        
        # Invalid type
        is_valid, error = field.validate_value("not a number")
        assert not is_valid
        assert "number" in error

    def test_select_field_validation(self):
        """Test select field validation."""
        from app.models.field import Field
        
        field = Field(
            name="Select Field",
            type=FieldType.SELECT,
            settings={"options": ["Option 1", "Option 2", "Option 3"]}
        )
        
        # Valid option
        is_valid, error = field.validate_value("Option 1")
        assert is_valid
        
        # Invalid option
        is_valid, error = field.validate_value("Invalid Option")
        assert not is_valid
        assert "must be one of" in error

    def test_checkbox_field_validation(self):
        """Test checkbox field validation."""
        from app.models.field import Field
        
        field = Field(
            name="Checkbox Field",
            type=FieldType.CHECKBOX,
            settings={}
        )
        
        # Valid boolean values
        is_valid, error = field.validate_value(True)
        assert is_valid
        
        is_valid, error = field.validate_value(False)
        assert is_valid
        
        # Invalid type
        is_valid, error = field.validate_value("not a boolean")
        assert not is_valid
        assert "true or false" in error


class TestRecordValidation:
    """Test record validation logic."""

    def test_record_property_validation(self):
        """Test record property validation against fields."""
        from app.models.field import Field
        from app.models.record import Record
        
        # Create test fields
        fields = [
            Field(
                id="field_1",
                name="Required Text",
                type=FieldType.TEXT,
                settings={"required": True}
            ),
            Field(
                id="field_2", 
                name="Optional Number",
                type=FieldType.NUMBER,
                settings={"required": False, "min_value": 0}
            )
        ]
        
        # Valid record
        record = Record(
            properties={
                "field_1": "test value",
                "field_2": 42
            }
        )
        
        is_valid, errors = record.validate_properties(fields)
        assert is_valid
        assert len(errors) == 0
        
        # Missing required field
        record = Record(
            properties={
                "field_2": 42
            }
        )
        
        is_valid, errors = record.validate_properties(fields)
        assert not is_valid
        assert any("Required field" in error for error in errors)
        
        # Invalid field value
        record = Record(
            properties={
                "field_1": "test value",
                "field_2": -1  # Below minimum
            }
        )
        
        is_valid, errors = record.validate_properties(fields)
        assert not is_valid
        assert any("at least" in error for error in errors)


if __name__ == "__main__":
    pytest.main([__file__])

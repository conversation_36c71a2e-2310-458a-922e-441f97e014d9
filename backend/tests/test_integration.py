"""Integration tests for the complete LLM-powered application."""

import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.models import Answer, ContentEmbedding, Image, ProcessedContent, Question, User
from tests.conftest import (
    create_test_audio_file,
    create_test_image,
    create_test_image_file,
    create_test_question,
    create_test_user,
    mock_authenticated_user,
)



class TestCompleteWorkflow:
    """Test complete workflow from content upload to intelligent querying."""

    @pytest.fixture
    def user(self, db: Session):
        """Create a test user."""
        return create_test_user(db)

    @pytest.mark.asyncio
    async def test_complete_image_workflow(self, db: Session, user: User):
        """Test complete workflow: upload image -> process -> search -> query."""
        with mock_authenticated_user(user):
            # Step 1: Upload image
            with patch("app.services.image_service.ImageService.process_image") as mock_process:
                mock_process.return_value = (b"compressed", b"thumbnail", (800, 600))

                with patch("app.services.image_service.ImageService.save_image_files") as mock_save:
                    mock_save.return_value = ("path", "thumb_path", "url", "thumb_url")

                    image_data = create_test_image_file()
                    upload_response = client.post(
                        "/images/upload",
                        files={"file": ("test.jpg", image_data, "image/jpeg")},
                        data={"description": "A beautiful landscape"},
                    )

                    assert upload_response.status_code == 201
                    image_id = upload_response.json()["id"]

            # Step 2: Process image with LLM
            with patch("app.services.llm_service.llm_service.analyze_image") as mock_analyze:
                mock_analyze.return_value = {
                    "description": "A scenic mountain landscape with trees",
                    "objects": ["mountain", "trees", "sky"],
                    "setting": "outdoor",
                    "text_content": "",
                    "tags": ["landscape", "nature", "mountain"],
                }

                with patch("app.services.llm_service.llm_service.generate_embeddings") as mock_embeddings:
                    mock_embeddings.return_value = [0.1] * 1536

                    process_response = client.post(
                        "/content/process", json={"content_ids": [image_id], "content_type": "image"}
                    )

                    assert process_response.status_code == 200

            # Step 3: Search for similar content
            with patch(
                "app.services.intelligent_query_service.intelligent_query_service.semantic_search"
            ) as mock_search:
                mock_embedding = MagicMock()
                mock_embedding.content_id = image_id
                mock_embedding.content_type.value = "image"
                mock_embedding.content_text = "A scenic mountain landscape with trees"
                mock_embedding.metadata = {"tags": ["landscape", "nature"]}
                mock_embedding.created_at.isoformat.return_value = "2024-01-01T00:00:00"

                mock_search.return_value = [(mock_embedding, 0.9)]

                search_response = client.post(
                    "/search/semantic", json={"query": "mountain landscape", "limit": 10}
                )

                assert search_response.status_code == 200
                results = search_response.json()["results"]
                assert len(results) == 1
                assert results[0]["similarity_score"] == 0.9

            # Step 4: Ask question about the content
            with patch(
                "app.services.intelligent_query_service.intelligent_query_service.answer_question_with_context"
            ) as mock_answer:
                mock_answer_obj = MagicMock()
                mock_answer_obj.content = "Based on your uploaded image, this appears to be a mountain landscape with trees and natural scenery."
                mock_answer_obj.confidence = 0.9
                mock_answer_obj.sources = ["Image analysis (similarity: 0.90)"]
                mock_answer_obj.llm_model = "gpt-4"
                mock_answer_obj.reasoning = "Answer based on semantic analysis of uploaded image"
                mock_answer_obj.id = "answer-id"
                mock_answer_obj.question_id = "question-id"

                mock_answer.return_value = mock_answer_obj

                question_response = client.post(
                    "/questions",
                    json={
                        "content": "What can you tell me about the landscapes I've uploaded?",
                        "type": "text",
                    },
                )

                assert question_response.status_code == 201
                question_id = question_response.json()["id"]

                # Get the answer
                answer_response = client.get(f"/answers/{question_id}")
                assert answer_response.status_code == 200
                answer_data = answer_response.json()
                assert "mountain landscape" in answer_data["content"].lower()

    @pytest.mark.asyncio
    async def test_complete_audio_workflow(self, db: Session, user: User):
        """Test complete workflow: upload audio -> transcribe -> process -> query."""
        with mock_authenticated_user(user):
            # Step 1: Upload audio question
            with patch("app.services.question_service.QuestionService.transcribe_audio") as mock_transcribe:
                mock_transcribe.return_value = "What is machine learning and how does it work?"

                with patch("app.services.llm_service.llm_service.process_text") as mock_process:
                    mock_process.return_value = {
                        "summary": "Question about machine learning concepts",
                        "key_points": ["machine learning", "how it works"],
                        "tags": ["AI", "ML", "technology"],
                        "sentiment": "neutral",
                    }

                    with patch("app.services.llm_service.llm_service.generate_embeddings") as mock_embeddings:
                        mock_embeddings.return_value = [0.2] * 1536

                        audio_data = create_test_audio_file()
                        audio_response = client.post(
                            "/questions/audio",
                            files={"file": ("question.mp3", audio_data, "audio/mpeg")},
                            data={"content": "Audio question about ML"},
                        )

                        assert audio_response.status_code == 201
                        question_id = audio_response.json()["id"]

            # Step 2: Answer the question with context
            with patch(
                "app.services.intelligent_query_service.intelligent_query_service.answer_question_with_context"
            ) as mock_answer:
                mock_answer_obj = MagicMock()
                mock_answer_obj.content = "Machine learning is a subset of AI that enables computers to learn from data without explicit programming."
                mock_answer_obj.confidence = 0.85
                mock_answer_obj.sources = ["AI Knowledge Base"]

                mock_answer.return_value = mock_answer_obj

                answer_response = client.get(f"/answers/{question_id}")
                assert answer_response.status_code == 200

    def test_multi_modal_context_integration(self, db: Session, user: User):
        """Test integration of multiple content types in context."""
        with mock_authenticated_user(user):
            # Create mixed content
            image = create_test_image(db, user_id=user.id, description="AI diagram")
            question = create_test_question(db, user_id=user.id, content="What is artificial intelligence?")

            # Mock embeddings for both
            with patch(
                "app.services.embeddings_service.embeddings_service.search_similar_content"
            ) as mock_search:
                mock_image_embedding = MagicMock()
                mock_image_embedding.content_type = "image"
                mock_image_embedding.content_text = "AI diagram showing neural networks"

                mock_text_embedding = MagicMock()
                mock_text_embedding.content_type = "text"
                mock_text_embedding.content_text = "What is artificial intelligence?"

                mock_search.return_value = [(mock_image_embedding, 0.9), (mock_text_embedding, 0.8)]

                search_response = client.post(
                    "/search/semantic", json={"query": "artificial intelligence concepts", "limit": 10}
                )

                assert search_response.status_code == 200
                results = search_response.json()["results"]

                # Should include both image and text content
                content_types = [r["content_type"] for r in results]
                assert "image" in content_types
                assert "text" in content_types


class TestErrorRecoveryAndResilience:
    """Test error recovery and system resilience."""

    @pytest.fixture
    def user(self, db: Session):
        """Create a test user."""
        return create_test_user(db)

    def test_llm_service_failure_recovery(self, db: Session, user: User):
        """Test graceful handling of LLM service failures."""
        with mock_authenticated_user(user):
            # Simulate LLM service failure
            with patch("app.services.llm_service.llm_service.generate_embeddings") as mock_embeddings:
                mock_embeddings.side_effect = Exception("OpenAI API unavailable")

                # Should fall back gracefully
                response = client.post("/search/semantic", json={"query": "test query", "limit": 10})

                assert response.status_code == 500
                assert "error" in response.json()["detail"].lower()

    def test_partial_processing_recovery(self, db: Session, user: User):
        """Test recovery from partial processing failures."""
        with mock_authenticated_user(user):
            image = create_test_image(db, user_id=user.id)

            # Simulate partial failure in batch processing
            with patch("app.services.content_processor.content_processor.process_image") as mock_process:
                mock_process.side_effect = Exception("Processing failed")

                response = client.post(
                    "/content/process", json={"content_ids": [str(image.id)], "content_type": "image"}
                )

                # Should report failures gracefully
                assert response.status_code == 200
                data = response.json()
                assert data["failed_count"] > 0

    def test_database_consistency_on_errors(self, db: Session, user: User):
        """Test database consistency when operations fail."""
        with mock_authenticated_user(user):
            # Simulate database error during embedding creation
            with patch("sqlalchemy.orm.Session.commit") as mock_commit:
                mock_commit.side_effect = Exception("Database error")

                response = client.post("/search/semantic", json={"query": "test query", "limit": 10})

                # Should handle database errors without corruption
                assert response.status_code in [500, 503]


class TestPerformanceAndScaling:
    """Test performance characteristics and scaling behavior."""

    @pytest.fixture
    def user(self, db: Session):
        """Create a test user."""
        return create_test_user(db)

    def test_large_content_processing(self, db: Session, user: User):
        """Test processing of large content volumes."""
        with mock_authenticated_user(user):
            # Create many content items
            content_ids = []
            for i in range(50):
                image = create_test_image(db, user_id=user.id, filename=f"test_{i}.jpg")
                content_ids.append(str(image.id))

            with patch("app.services.content_processor.content_processor.process_image") as mock_process:
                mock_process.return_value = (MagicMock(), MagicMock())

                response = client.post(
                    "/content/process", json={"content_ids": content_ids, "content_type": "image"}
                )

                assert response.status_code == 200
                data = response.json()
                assert data["processed_count"] <= 50  # Should handle large batches

    def test_search_performance_with_many_embeddings(self, db: Session, user: User):
        """Test search performance with large embedding datasets."""
        with mock_authenticated_user(user):
            # Mock large result set
            with patch(
                "app.services.embeddings_service.embeddings_service.search_similar_content"
            ) as mock_search:
                # Simulate many results
                mock_results = []
                for i in range(1000):
                    mock_embedding = MagicMock()
                    mock_embedding.content_id = f"content-{i}"
                    mock_embedding.content_type.value = "text"
                    mock_embedding.content_text = f"Content {i}"
                    mock_embedding.metadata = {}
                    mock_embedding.created_at.isoformat.return_value = "2024-01-01T00:00:00"
                    mock_results.append((mock_embedding, 0.9 - i * 0.001))

                mock_search.return_value = mock_results[:20]  # Return top 20

                response = client.post("/search/semantic", json={"query": "test query", "limit": 20})

                assert response.status_code == 200
                data = response.json()
                assert len(data["results"]) == 20

    def test_concurrent_request_handling(self, db: Session, user: User):
        """Test handling of concurrent requests."""
        with mock_authenticated_user(user):
            import threading
            import time

            results = []

            def make_request():
                response = client.get("/users/profile")
                results.append(response.status_code)

            # Create multiple threads
            threads = []
            for i in range(10):
                thread = threading.Thread(target=make_request)
                threads.append(thread)

            # Start all threads
            for thread in threads:
                thread.start()

            # Wait for completion
            for thread in threads:
                thread.join()

            # All requests should succeed
            assert all(status == 200 for status in results)


class TestDataConsistencyAndIntegrity:
    """Test data consistency and integrity across operations."""

    @pytest.fixture
    def user(self, db: Session):
        """Create a test user."""
        return create_test_user(db)

    def test_embedding_content_consistency(self, db: Session, user: User):
        """Test consistency between content and embeddings."""
        with mock_authenticated_user(user):
            question = create_test_question(db, user_id=user.id, content="Original content")

            # Process content
            with patch("app.services.llm_service.llm_service.process_text") as mock_process:
                mock_process.return_value = {
                    "summary": "Processed summary",
                    "key_points": ["point1"],
                    "tags": ["tag1"],
                    "sentiment": "neutral",
                }

                with patch("app.services.llm_service.llm_service.generate_embeddings") as mock_embeddings:
                    mock_embeddings.return_value = [0.1] * 1536

                    response = client.post(
                        "/content/process", json={"content_ids": [str(question.id)], "content_type": "text"}
                    )

                    assert response.status_code == 200

            # Verify processed content exists
            processed_response = client.get("/content/processed?content_type=text")
            assert processed_response.status_code == 200
            processed_data = processed_response.json()
            assert len(processed_data) > 0

            # Verify embedding exists
            embeddings_response = client.get("/content/embeddings?content_type=text")
            assert embeddings_response.status_code == 200
            embeddings_data = embeddings_response.json()
            assert len(embeddings_data) > 0

    def test_user_data_isolation_integrity(self, client: TestClient, db: Session):
        """Test that user data isolation is maintained across all operations."""
        user1 = create_test_user(db, email="<EMAIL>")
        user2 = create_test_user(db, email="<EMAIL>")

        # Create content for both users
        question1 = create_test_question(db, user_id=user1.id, content="User 1 question")
        question2 = create_test_question(db, user_id=user2.id, content="User 2 question")

        # Test user 1 can only see their content
        with mock_authenticated_user(user1):
            questions_response = client.get("/questions")
            assert questions_response.status_code == 200
            questions_data = questions_response.json()["questions"]

            # Should only see user1's questions
            question_contents = [q["content"] for q in questions_data]
            assert "User 1 question" in question_contents
            assert "User 2 question" not in question_contents

        # Test user 2 can only see their content
        with mock_authenticated_user(user2):
            questions_response = client.get("/questions")
            assert questions_response.status_code == 200
            questions_data = questions_response.json()["questions"]

            # Should only see user2's questions
            question_contents = [q["content"] for q in questions_data]
            assert "User 2 question" in question_contents
            assert "User 1 question" not in question_contents

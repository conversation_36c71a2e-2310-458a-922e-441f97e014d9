"""Database schemas."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from .common import PaginationInfo
from .field import FieldResponse
from .record import RecordResponse


class DatabaseRequest(BaseModel):
    """Database creation/update request schema."""

    name: str = Field(..., description="Database name", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Database description")
    icon: Optional[str] = Field(None, description="Database icon (emoji or icon name)", max_length=100)
    color: Optional[str] = Field(None, description="Database color (hex code)", pattern=r"^#[0-9A-Fa-f]{6}$")


class DatabaseResponse(BaseModel):
    """Database response schema."""

    id: UUID = Field(..., description="Database unique identifier")
    name: str = Field(..., description="Database name")
    description: Optional[str] = Field(None, description="Database description")
    icon: Optional[str] = Field(None, description="Database icon")
    color: Optional[str] = Field(None, description="Database color")
    owner_id: UUID = Field(..., description="Database owner ID")
    created_at: datetime = Field(..., description="Database creation timestamp")
    updated_at: datetime = Field(..., description="Database last update timestamp")

    model_config = ConfigDict(from_attributes=True)


class DatabaseDetailResponse(DatabaseResponse):
    """Database detail response with fields and records."""

    fields: List[FieldResponse] = Field(default=[], description="Database fields")
    records: Optional[List[RecordResponse]] = Field(None, description="Database records (optional)")


class DatabaseListResponse(BaseModel):
    """Database list response schema."""

    databases: List[DatabaseResponse] = Field(..., description="List of databases")
    pagination: PaginationInfo = Field(..., description="Pagination information")


class DatabaseStatsResponse(BaseModel):
    """Database statistics response schema."""

    id: UUID = Field(..., description="Database unique identifier")
    name: str = Field(..., description="Database name")
    field_count: int = Field(..., description="Number of fields")
    record_count: int = Field(..., description="Number of records")
    created_at: datetime = Field(..., description="Database creation timestamp")
    updated_at: datetime = Field(..., description="Database last update timestamp")


class DatabaseUpdateRequest(BaseModel):
    """Database update request schema."""

    name: Optional[str] = Field(None, description="Database name", min_length=1, max_length=255)
    description: Optional[str] = Field(None, description="Database description")
    icon: Optional[str] = Field(None, description="Database icon", max_length=100)
    color: Optional[str] = Field(None, description="Database color", pattern=r"^#[0-9A-Fa-f]{6}$")


class DatabaseCloneRequest(BaseModel):
    """Database clone request schema."""

    name: str = Field(..., description="New database name", min_length=1, max_length=255)
    include_records: bool = Field(False, description="Whether to clone records as well")
    description: Optional[str] = Field(None, description="New database description")

"""Field schemas."""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator

from app.models.field import FieldType


class FieldSettings(BaseModel):
    """Base field settings schema."""

    required: bool = Field(False, description="Whether the field is required")


class TextFieldSettings(FieldSettings):
    """Text field settings schema."""

    max_length: Optional[int] = Field(None, description="Maximum text length", ge=1)


class NumberFieldSettings(FieldSettings):
    """Number field settings schema."""

    min_value: Optional[float] = Field(None, description="Minimum value")
    max_value: Optional[float] = Field(None, description="Maximum value")
    decimal_places: int = Field(0, description="Number of decimal places", ge=0, le=10)


class DateFieldSettings(FieldSettings):
    """Date field settings schema."""

    include_time: bool = Field(False, description="Whether to include time")


class SelectFieldSettings(FieldSettings):
    """Select field settings schema."""

    options: List[str] = Field(..., description="Available options", min_length=1)


class MultiSelectFieldSettings(FieldSettings):
    """Multi-select field settings schema."""

    options: List[str] = Field(..., description="Available options", min_length=1)


class CheckboxFieldSettings(FieldSettings):
    """Checkbox field settings schema."""

    default_value: bool = Field(False, description="Default checkbox value")


class FieldRequest(BaseModel):
    """Field creation request schema."""

    name: str = Field(..., description="Field name", min_length=1, max_length=255)
    type: FieldType = Field(..., description="Field type")
    settings: Optional[Dict[str, Any]] = Field(None, description="Field-specific settings")
    position: int = Field(0, description="Field position/order", ge=0)

    @field_validator("settings")
    @classmethod
    def validate_settings(cls, v, info):
        """Validate settings based on field type."""
        if v is None:
            return v

        field_type = info.data.get("type") if info.data else None
        if not field_type:
            return v

        # Basic validation - could be enhanced with more specific checks
        if field_type in [FieldType.SELECT, FieldType.MULTI_SELECT]:
            if "options" not in v or not isinstance(v["options"], list):
                raise ValueError(f"{field_type.value} field must have 'options' list in settings")
            if len(v["options"]) == 0:
                raise ValueError(f"{field_type.value} field must have at least one option")

        return v


class FieldResponse(BaseModel):
    """Field response schema."""

    id: UUID = Field(..., description="Field unique identifier")
    name: str = Field(..., description="Field name")
    type: FieldType = Field(..., description="Field type")
    settings: Dict[str, Any] = Field(default_factory=dict, description="Field settings")
    position: int = Field(..., description="Field position")
    database_id: UUID = Field(..., description="Database ID")
    created_at: datetime = Field(..., description="Field creation timestamp")
    updated_at: datetime = Field(..., description="Field last update timestamp")

    model_config = ConfigDict(from_attributes=True)


class FieldUpdateRequest(BaseModel):
    """Field update request schema."""

    name: Optional[str] = Field(None, description="Field name", min_length=1, max_length=255)
    settings: Optional[Dict[str, Any]] = Field(None, description="Field settings")
    position: Optional[int] = Field(None, description="Field position", ge=0)

    @field_validator("settings")
    @classmethod
    def validate_settings(cls, v):
        """Basic settings validation."""
        if v is None:
            return v

        # Ensure settings is a dictionary
        if not isinstance(v, dict):
            raise ValueError("Settings must be a dictionary")

        return v


class FieldBulkUpdateRequest(BaseModel):
    """Bulk field update request schema."""

    fields: List[Dict[str, Any]] = Field(..., description="List of field updates")

    @field_validator("fields")
    @classmethod
    def validate_fields(cls, v):
        """Validate field updates."""
        if not v:
            raise ValueError("At least one field update is required")

        for field_update in v:
            if "id" not in field_update:
                raise ValueError("Each field update must include 'id'")

        return v


class FieldReorderRequest(BaseModel):
    """Field reorder request schema."""

    field_ids: List[UUID] = Field(..., description="Ordered list of field IDs", min_length=1)


class FieldValidationResponse(BaseModel):
    """Field validation response schema."""

    is_valid: bool = Field(..., description="Whether the value is valid")
    error: Optional[str] = Field(None, description="Validation error message")
    formatted_value: Optional[Any] = Field(None, description="Formatted/normalized value")

"""API schemas."""

from .answer import AnswerResponse
from .auth import (
    AppleAuthRequest,
    AuthResponse,
    GoogleAuthRequest,
    MicrosoftAuthRequest,
    RefreshTokenRequest,
    TokenResponse,
)
from .common import HealthResponse, MessageResponse, PaginationInfo
from .database import (
    DatabaseCloneRequest,
    DatabaseDetailResponse,
    DatabaseListResponse,
    DatabaseRequest,
    DatabaseResponse,
    DatabaseStatsResponse,
    DatabaseUpdateRequest,
)
from .field import (
    FieldBulkUpdateRequest,
    FieldReorderRequest,
    FieldRequest,
    FieldResponse,
    FieldUpdateRequest,
    FieldValidationResponse,
)
from .image import ImageResponse, ImageUploadResponse
from .question import (
    AudioQuestionRequest,
    QuestionDetailResponse,
    QuestionListResponse,
    QuestionRequest,
    QuestionResponse,
)
from .record import (
    RecordBulkCreateRequest,
    RecordBulkDeleteRequest,
    RecordBulkUpdateRequest,
    RecordDetailResponse,
    RecordExportRequest,
    RecordFilterRequest,
    RecordImportRequest,
    RecordListResponse,
    RecordRequest,
    RecordResponse,
    RecordUpdateRequest,
    RecordValidationResponse,
)
from .user import UpdateUserProfile, UserProfile

__all__ = [
    # Auth
    "AppleAuthRequest",
    "AuthResponse",
    "GoogleAuthRequest",
    "MicrosoftAuthRequest",
    "RefreshTokenRequest",
    "TokenResponse",
    # User
    "UpdateUserProfile",
    "UserProfile",
    # Image
    "ImageResponse",
    "ImageUploadResponse",
    # Question
    "AudioQuestionRequest",
    "QuestionRequest",
    "QuestionResponse",
    "QuestionDetailResponse",
    "QuestionListResponse",
    # Answer
    "AnswerResponse",
    # Database
    "DatabaseRequest",
    "DatabaseResponse",
    "DatabaseDetailResponse",
    "DatabaseListResponse",
    "DatabaseStatsResponse",
    "DatabaseUpdateRequest",
    "DatabaseCloneRequest",
    # Field
    "FieldRequest",
    "FieldResponse",
    "FieldUpdateRequest",
    "FieldBulkUpdateRequest",
    "FieldReorderRequest",
    "FieldValidationResponse",
    # Record
    "RecordRequest",
    "RecordResponse",
    "RecordDetailResponse",
    "RecordUpdateRequest",
    "RecordListResponse",
    "RecordFilterRequest",
    "RecordBulkCreateRequest",
    "RecordBulkUpdateRequest",
    "RecordBulkDeleteRequest",
    "RecordExportRequest",
    "RecordImportRequest",
    "RecordValidationResponse",
    # Common
    "MessageResponse",
    "PaginationInfo",
    "HealthResponse",
]

"""Authentication schemas."""

from typing import Dict, Optional

from pydantic import BaseModel, Field

from .user import UserProfile


class AppleAuthRequest(BaseModel):
    """Apple Sign-In authentication request."""

    identity_token: str = Field(..., description="Apple ID token")
    authorization_code: str = Field(..., description="Apple authorization code")
    user_info: Optional[Dict] = Field(None, description="Optional user info from Apple")


class GoogleAuthRequest(BaseModel):
    """Google Sign-In authentication request."""

    id_token: str = Field(..., description="Google ID token")
    access_token: Optional[str] = Field(None, description="Google access token")


class MicrosoftAuthRequest(BaseModel):
    """Microsoft Sign-In authentication request."""

    access_token: str = Field(..., description="Microsoft access token")
    id_token: Optional[str] = Field(None, description="Microsoft ID token")


class RefreshTokenRequest(BaseModel):
    """Refresh token request."""

    refresh_token: str = Field(..., description="Refresh token")


class TokenResponse(BaseModel):
    """Token response."""

    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")


class AuthResponse(BaseModel):
    """Authentication response."""

    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="Refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: UserProfile = Field(..., description="User profile information")

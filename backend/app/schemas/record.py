"""Record schemas."""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator

from .common import PaginationInfo


class RecordRequest(BaseModel):
    """Record creation request schema."""

    properties: Dict[str, Any] = Field(..., description="Record properties by field ID")

    @field_validator("properties")
    @classmethod
    def validate_properties(cls, v):
        """Validate properties structure."""
        if not isinstance(v, dict):
            raise ValueError("Properties must be a dictionary")
        return v


class RecordResponse(BaseModel):
    """Record response schema."""

    id: UUID = Field(..., description="Record unique identifier")
    properties: Dict[str, Any] = Field(..., description="Record properties")
    database_id: UUID = Field(..., description="Database ID")
    created_by: UUID = Field(..., description="Record creator ID")
    created_at: datetime = Field(..., description="Record creation timestamp")
    updated_at: datetime = Field(..., description="Record last update timestamp")

    model_config = ConfigDict(from_attributes=True)


class RecordDetailResponse(RecordResponse):
    """Record detail response with field names."""

    properties_with_names: Optional[Dict[str, Any]] = Field(
        None, description="Properties with field names instead of IDs"
    )


class RecordUpdateRequest(BaseModel):
    """Record update request schema."""

    properties: Dict[str, Any] = Field(..., description="Updated record properties")

    @field_validator("properties")
    @classmethod
    def validate_properties(cls, v):
        """Validate properties structure."""
        if not isinstance(v, dict):
            raise ValueError("Properties must be a dictionary")
        return v


class RecordListResponse(BaseModel):
    """Record list response schema."""

    records: List[RecordResponse] = Field(..., description="List of records")
    pagination: PaginationInfo = Field(..., description="Pagination information")


class RecordFilterRequest(BaseModel):
    """Record filter request schema."""

    filters: List[Dict[str, Any]] = Field(default=[], description="List of filters")
    search: Optional[str] = Field(None, description="Full-text search query")
    sort_by: Optional[str] = Field(None, description="Field ID to sort by")
    sort_order: str = Field("asc", description="Sort order: 'asc' or 'desc'")
    page: int = Field(1, description="Page number", ge=1)
    page_size: int = Field(20, description="Page size", ge=1, le=100)

    @field_validator("sort_order")
    @classmethod
    def validate_sort_order(cls, v):
        """Validate sort order."""
        if v not in ["asc", "desc"]:
            raise ValueError("Sort order must be 'asc' or 'desc'")
        return v

    @field_validator("filters")
    @classmethod
    def validate_filters(cls, v):
        """Validate filter structure."""
        for filter_item in v:
            if not isinstance(filter_item, dict):
                raise ValueError("Each filter must be a dictionary")

            required_keys = ["field_id", "operator", "value"]
            for key in required_keys:
                if key not in filter_item:
                    raise ValueError(f"Filter must include '{key}'")

            # Validate operator
            valid_operators = [
                "equals", "not_equals", "contains", "not_contains",
                "starts_with", "ends_with", "is_empty", "is_not_empty",
                "greater_than", "less_than", "greater_equal", "less_equal",
                "in", "not_in"
            ]
            if filter_item["operator"] not in valid_operators:
                raise ValueError(f"Invalid operator: {filter_item['operator']}")

        return v


class RecordBulkCreateRequest(BaseModel):
    """Bulk record creation request schema."""

    records: List[RecordRequest] = Field(..., description="List of records to create", min_length=1, max_length=100)


class RecordBulkUpdateRequest(BaseModel):
    """Bulk record update request schema."""

    updates: List[Dict[str, Any]] = Field(..., description="List of record updates", min_length=1, max_length=100)

    @field_validator("updates")
    @classmethod
    def validate_updates(cls, v):
        """Validate update structure."""
        for update in v:
            if "id" not in update or "properties" not in update:
                raise ValueError("Each update must include 'id' and 'properties'")
        return v


class RecordBulkDeleteRequest(BaseModel):
    """Bulk record deletion request schema."""

    record_ids: List[UUID] = Field(..., description="List of record IDs to delete", min_length=1, max_length=100)


class RecordExportRequest(BaseModel):
    """Record export request schema."""

    format: str = Field("csv", description="Export format: 'csv' or 'json'")
    include_field_names: bool = Field(True, description="Include field names in export")
    filters: Optional[List[Dict[str, Any]]] = Field(None, description="Filters to apply before export")

    @field_validator("format")
    @classmethod
    def validate_format(cls, v):
        """Validate export format."""
        if v not in ["csv", "json"]:
            raise ValueError("Format must be 'csv' or 'json'")
        return v


class RecordImportRequest(BaseModel):
    """Record import request schema."""

    format: str = Field("csv", description="Import format: 'csv' or 'json'")
    data: str = Field(..., description="Import data as string")
    field_mapping: Optional[Dict[str, str]] = Field(
        None, description="Mapping from import columns to field IDs"
    )
    skip_validation: bool = Field(False, description="Skip field validation during import")

    @field_validator("format")
    @classmethod
    def validate_format(cls, v):
        """Validate import format."""
        if v not in ["csv", "json"]:
            raise ValueError("Format must be 'csv' or 'json'")
        return v


class RecordValidationResponse(BaseModel):
    """Record validation response schema."""

    is_valid: bool = Field(..., description="Whether the record is valid")
    errors: List[str] = Field(default=[], description="List of validation errors")
    warnings: List[str] = Field(default=[], description="List of validation warnings")

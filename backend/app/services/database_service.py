"""Database service for managing customizable databases."""

import uuid
from typing import List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, func
from sqlalchemy.orm import Session, joinedload

from app.models import Database, Field, Record, User
from app.schemas.database import DatabaseRequest, DatabaseUpdateRequest


class DatabaseService:
    """Service for managing customizable databases."""

    def __init__(self, db: Session):
        self.db = db

    def create_database(self, user_id: UUID, database_data: DatabaseRequest) -> Database:
        """Create a new database."""
        database = Database(
            owner_id=user_id,
            name=database_data.name,
            description=database_data.description,
            icon=database_data.icon,
            color=database_data.color,
        )
        
        self.db.add(database)
        self.db.commit()
        self.db.refresh(database)
        
        return database

    def get_database(self, database_id: UUID, user_id: UUID) -> Optional[Database]:
        """Get a database by ID for a specific user."""
        return (
            self.db.query(Database)
            .filter(and_(Database.id == database_id, Database.owner_id == user_id))
            .options(joinedload(Database.fields), joinedload(Database.records))
            .first()
        )

    def get_database_with_fields(self, database_id: UUID, user_id: UUID) -> Optional[Database]:
        """Get a database with its fields."""
        return (
            self.db.query(Database)
            .filter(and_(Database.id == database_id, Database.owner_id == user_id))
            .options(joinedload(Database.fields))
            .first()
        )

    def get_user_databases(
        self, user_id: UUID, skip: int = 0, limit: int = 20
    ) -> Tuple[List[Database], int]:
        """Get all databases for a user with pagination."""
        query = self.db.query(Database).filter(Database.owner_id == user_id)
        
        total = query.count()
        databases = query.offset(skip).limit(limit).all()
        
        return databases, total

    def update_database(
        self, database_id: UUID, user_id: UUID, update_data: DatabaseUpdateRequest
    ) -> Optional[Database]:
        """Update a database."""
        database = self.get_database(database_id, user_id)
        if not database:
            return None

        update_dict = update_data.dict(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(database, field, value)

        self.db.commit()
        self.db.refresh(database)
        
        return database

    def delete_database(self, database_id: UUID, user_id: UUID) -> bool:
        """Delete a database."""
        database = self.get_database(database_id, user_id)
        if not database:
            return False

        self.db.delete(database)
        self.db.commit()
        
        return True

    def clone_database(
        self, database_id: UUID, user_id: UUID, new_name: str, 
        include_records: bool = False, description: Optional[str] = None
    ) -> Optional[Database]:
        """Clone a database with its fields and optionally records."""
        original_db = self.get_database(database_id, user_id)
        if not original_db:
            return None

        # Create new database
        new_database = Database(
            owner_id=user_id,
            name=new_name,
            description=description or original_db.description,
            icon=original_db.icon,
            color=original_db.color,
        )
        
        self.db.add(new_database)
        self.db.flush()  # Get the ID without committing

        # Clone fields
        field_id_mapping = {}
        for field in original_db.fields:
            new_field = Field(
                database_id=new_database.id,
                name=field.name,
                type=field.type,
                settings=field.settings,
                position=field.position,
            )
            self.db.add(new_field)
            self.db.flush()
            field_id_mapping[str(field.id)] = str(new_field.id)

        # Clone records if requested
        if include_records:
            for record in original_db.records:
                # Map old field IDs to new field IDs in properties
                new_properties = {}
                for old_field_id, value in (record.properties or {}).items():
                    new_field_id = field_id_mapping.get(old_field_id, old_field_id)
                    new_properties[new_field_id] = value

                new_record = Record(
                    database_id=new_database.id,
                    properties=new_properties,
                    created_by=user_id,
                )
                self.db.add(new_record)

        self.db.commit()
        self.db.refresh(new_database)
        
        return new_database

    def get_database_stats(self, user_id: UUID) -> List[dict]:
        """Get statistics for all user databases."""
        stats = (
            self.db.query(
                Database.id,
                Database.name,
                Database.created_at,
                Database.updated_at,
                func.count(Field.id).label("field_count"),
                func.count(Record.id).label("record_count"),
            )
            .outerjoin(Field, Database.id == Field.database_id)
            .outerjoin(Record, Database.id == Record.database_id)
            .filter(Database.owner_id == user_id)
            .group_by(Database.id, Database.name, Database.created_at, Database.updated_at)
            .all()
        )

        return [
            {
                "id": stat.id,
                "name": stat.name,
                "field_count": stat.field_count or 0,
                "record_count": stat.record_count or 0,
                "created_at": stat.created_at,
                "updated_at": stat.updated_at,
            }
            for stat in stats
        ]

    def search_databases(self, user_id: UUID, query: str) -> List[Database]:
        """Search databases by name or description."""
        return (
            self.db.query(Database)
            .filter(
                and_(
                    Database.owner_id == user_id,
                    Database.name.ilike(f"%{query}%") | Database.description.ilike(f"%{query}%")
                )
            )
            .all()
        )

    def get_recent_databases(self, user_id: UUID, limit: int = 5) -> List[Database]:
        """Get recently updated databases."""
        return (
            self.db.query(Database)
            .filter(Database.owner_id == user_id)
            .order_by(Database.updated_at.desc())
            .limit(limit)
            .all()
        )

    def validate_database_access(self, database_id: UUID, user_id: UUID) -> bool:
        """Validate that a user has access to a database."""
        return (
            self.db.query(Database)
            .filter(and_(Database.id == database_id, Database.owner_id == user_id))
            .first()
            is not None
        )

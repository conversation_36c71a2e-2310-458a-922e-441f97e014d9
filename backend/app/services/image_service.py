"""Image processing and storage service."""

import os
import uuid
from io import Bytes<PERSON>
from pathlib import Path
from typing import Optional, <PERSON><PERSON>

import aiofiles
from fastapi import HTT<PERSON>Ex<PERSON>, UploadFile, status
from PIL import Image as PILImage

from app.config import settings
from app.services.supabase_service import supabase_service


class ImageService:
    """Service for handling image upload, processing, and storage."""

    @staticmethod
    def validate_image_file(file: UploadFile) -> None:
        """Validate uploaded image file."""
        # Check file size
        if file.size and file.size > settings.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size exceeds maximum allowed size of {settings.MAX_FILE_SIZE} bytes",
            )

        # Check file extension
        if file.filename:
            file_extension = file.filename.split(".")[-1].lower()
            if file_extension not in settings.allowed_image_extensions_list:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File type not allowed. Allowed types: {', '.join(settings.allowed_image_extensions_list)}",
                )

        # Check content type
        if file.content_type and not file.content_type.startswith("image/"):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="File must be an image")

    @staticmethod
    async def process_image(file_content: bytes, filename: str) -> Tuple[bytes, bytes, Tuple[int, int]]:
        """Process image: compress and create thumbnail."""
        try:
            # Open image
            image = PILImage.open(BytesIO(file_content))

            # Convert to RGB if necessary
            if image.mode in ("RGBA", "P"):
                image = image.convert("RGB")

            # Get original dimensions
            original_size = image.size

            # Compress original image
            compressed_buffer = BytesIO()
            image.save(compressed_buffer, format="JPEG", quality=settings.IMAGE_QUALITY, optimize=True)
            compressed_content = compressed_buffer.getvalue()

            # Create thumbnail
            thumbnail = image.copy()
            thumbnail.thumbnail(settings.thumbnail_size_tuple, PILImage.Resampling.LANCZOS)

            thumbnail_buffer = BytesIO()
            thumbnail.save(thumbnail_buffer, format="JPEG", quality=settings.IMAGE_QUALITY, optimize=True)
            thumbnail_content = thumbnail_buffer.getvalue()

            return compressed_content, thumbnail_content, original_size

        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail=f"Error processing image: {str(e)}"
            )

    @staticmethod
    async def save_image_files(
        image_content: bytes, thumbnail_content: bytes, filename: str, user_id: str
    ) -> Tuple[str, str, str, str]:
        """Save image and thumbnail files to storage (Supabase or local)."""
        # Generate unique filenames
        file_extension = filename.split(".")[-1].lower()
        unique_filename = f"{uuid.uuid4()}.{file_extension}"
        thumbnail_filename = f"{uuid.uuid4()}_thumb.jpg"

        # Try Supabase Storage first
        if settings.USE_SUPABASE and supabase_service.is_available():
            try:
                # Ensure buckets exist
                await supabase_service.create_bucket("images", public=True)
                await supabase_service.create_bucket("thumbnails", public=True)

                # Upload to Supabase Storage
                image_path = f"{user_id}/{unique_filename}"
                thumbnail_path = f"{user_id}/{thumbnail_filename}"

                image_url = await supabase_service.upload_file("images", image_path, image_content)
                thumbnail_url = await supabase_service.upload_file(
                    "thumbnails", thumbnail_path, thumbnail_content
                )

                if image_url and thumbnail_url:
                    return image_path, thumbnail_path, image_url, thumbnail_url

            except Exception as e:
                # Fall back to local storage if Supabase fails
                print(f"Supabase upload failed, falling back to local storage: {e}")

        # Local storage fallback
        # Create upload directory if it doesn't exist
        upload_dir = Path(settings.UPLOAD_DIR)
        upload_dir.mkdir(exist_ok=True)

        # Create user-specific directory
        user_dir = upload_dir / str(user_id)
        user_dir.mkdir(exist_ok=True)

        # File paths
        image_path = user_dir / unique_filename
        thumbnail_path = user_dir / thumbnail_filename

        try:
            # Save main image
            async with aiofiles.open(image_path, "wb") as f:
                await f.write(image_content)

            # Save thumbnail
            async with aiofiles.open(thumbnail_path, "wb") as f:
                await f.write(thumbnail_content)

            # Generate URLs
            base_url = f"/uploads/{user_id}"
            image_url = f"{base_url}/{unique_filename}"
            thumbnail_url = f"{base_url}/{thumbnail_filename}"

            return str(image_path), str(thumbnail_path), image_url, thumbnail_url

        except Exception as e:
            # Clean up files if something went wrong
            if image_path.exists():
                image_path.unlink()
            if thumbnail_path.exists():
                thumbnail_path.unlink()

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error saving image files: {str(e)}",
            )

    @staticmethod
    async def delete_image_files(image_path: str, thumbnail_path: Optional[str] = None) -> None:
        """Delete image files from storage (Supabase or local)."""
        try:
            # Try Supabase Storage first
            if settings.USE_SUPABASE and supabase_service.is_available():
                # Extract file path from full path for Supabase
                if "/" in image_path:
                    supabase_image_path = image_path
                    supabase_thumbnail_path = thumbnail_path
                else:
                    # Local path, extract filename
                    supabase_image_path = os.path.basename(image_path)
                    supabase_thumbnail_path = os.path.basename(thumbnail_path) if thumbnail_path else None

                # Delete from Supabase Storage
                await supabase_service.delete_file("images", supabase_image_path)
                if supabase_thumbnail_path:
                    await supabase_service.delete_file("thumbnails", supabase_thumbnail_path)

            # Also try local deletion (for backward compatibility)
            if os.path.exists(image_path):
                os.remove(image_path)

            if thumbnail_path and os.path.exists(thumbnail_path):
                os.remove(thumbnail_path)

        except Exception:
            # Log error but don't raise exception
            # File deletion failures shouldn't break the API
            pass

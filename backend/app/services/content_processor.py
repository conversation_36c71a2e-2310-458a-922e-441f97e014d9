"""Content processing service for LLM-powered analysis and embedding generation."""

import asyncio
import logging
from typing import Dict, List, Optional, Tuple
from uuid import UUID

from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.orm import Session

from app.models import (
    ContentEmbedding,
    ContentType,
    Image,
    ProcessedContent,
    ProcessingStatus,
    Question,
    User,
)
from app.services.embeddings_service import embeddings_service
from app.services.llm_service import llm_service

logger = logging.getLogger(__name__)


class ContentProcessor:
    """Service for processing content with LLM and generating embeddings."""

    @staticmethod
    async def process_image(
        db: Session, image: Image, user: User
    ) -> Tuple[ProcessedContent, ContentEmbedding]:
        """Process an image with LLM analysis and create embeddings."""
        try:
            # Update processing status
            image.processing_status = ProcessingStatus.PROCESSING
            db.commit()

            # Read image content (this would need to be implemented based on your storage)
            # For now, we'll use the image URL or file path
            image_content = await ContentProcessor._get_image_content(image)

            # Analyze image with LLM
            analysis_result = await llm_service.analyze_image(image_content)

            # Extract information from analysis
            description = analysis_result.get("description", "")
            objects = analysis_result.get("objects", [])
            setting = analysis_result.get("setting", "")
            text_content = analysis_result.get("text_content", "")
            tags = analysis_result.get("tags", [])

            # Create comprehensive text for embedding
            embedding_text = f"""
            Image Description: {description}
            Objects: {', '.join(objects) if objects else 'None'}
            Setting: {setting}
            Text in Image: {text_content}
            Tags: {', '.join(tags) if tags else 'None'}
            Original Description: {image.description or 'None'}
            """

            # Create processed content record
            processed_content = ProcessedContent(
                content_id=image.id,
                content_type=ContentType.IMAGE,
                original_content=image.description,
                processed_content=embedding_text,
                summary=description,
                key_points=objects,
                tags=tags,
                llm_model="gpt-4-vision-preview",
                processing_metadata={
                    "setting": setting,
                    "text_content": text_content,
                    "analysis_result": analysis_result,
                },
                user_id=user.id,
            )

            db.add(processed_content)

            # Update image with LLM results
            image.llm_description = description
            image.llm_tags = tags
            image.processing_status = ProcessingStatus.COMPLETED

            # Create embedding
            embedding = await embeddings_service.create_embedding(
                db=db,
                content_id=image.id,
                content_type=ContentType.IMAGE,
                content_text=embedding_text,
                user_id=user.id,
                metadata={"description": description, "objects": objects, "setting": setting, "tags": tags},
            )

            db.commit()

            logger.info(f"Successfully processed image {image.id}")
            return processed_content, embedding

        except Exception as e:
            logger.error(f"Error processing image {image.id}: {str(e)}")
            image.processing_status = ProcessingStatus.FAILED
            db.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error processing image: {str(e)}"
            )

    @staticmethod
    async def process_audio_question(
        db: Session, question: Question, audio_content: bytes, user: User
    ) -> Tuple[ProcessedContent, ContentEmbedding]:
        """Process audio question with transcription and analysis."""
        try:
            # Transcribe audio
            transcribed_text = await llm_service.transcribe_audio(audio_content, question.audio_filename)

            # Process the transcribed text
            analysis_result = await llm_service.process_text(transcribed_text, task="analyze")

            # Update question with processed content
            question.llm_processed_content = transcribed_text

            # Create processed content record
            processed_content = ProcessedContent(
                content_id=question.id,
                content_type=ContentType.AUDIO,
                original_content=question.content,
                processed_content=transcribed_text,
                summary=analysis_result.get("summary", transcribed_text[:200]),
                key_points=analysis_result.get("key_points", []),
                tags=analysis_result.get("tags", []),
                sentiment=analysis_result.get("sentiment", "neutral"),
                llm_model="whisper-1",
                processing_metadata={"transcription": transcribed_text, "analysis_result": analysis_result},
                user_id=user.id,
            )

            db.add(processed_content)

            # Create embedding
            embedding = await embeddings_service.create_embedding(
                db=db,
                content_id=question.id,
                content_type=ContentType.AUDIO,
                content_text=transcribed_text,
                user_id=user.id,
                metadata={
                    "original_question": question.content,
                    "transcription": transcribed_text,
                    "tags": analysis_result.get("tags", []),
                },
            )

            db.commit()

            logger.info(f"Successfully processed audio question {question.id}")
            return processed_content, embedding

        except Exception as e:
            logger.error(f"Error processing audio question {question.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error processing audio: {str(e)}"
            )

    @staticmethod
    async def process_text_question(
        db: Session, question: Question, user: User
    ) -> Tuple[ProcessedContent, ContentEmbedding]:
        """Process text question with LLM analysis."""
        try:
            # Analyze text with LLM
            analysis_result = await llm_service.process_text(question.content, task="analyze")

            # Update question with processed content
            question.llm_processed_content = question.content

            # Create processed content record
            processed_content = ProcessedContent(
                content_id=question.id,
                content_type=ContentType.TEXT,
                original_content=question.content,
                processed_content=question.content,
                summary=analysis_result.get("summary", question.content[:200]),
                key_points=analysis_result.get("key_points", []),
                tags=analysis_result.get("tags", []),
                sentiment=analysis_result.get("sentiment", "neutral"),
                llm_model="gpt-4",
                processing_metadata={"analysis_result": analysis_result},
                user_id=user.id,
            )

            db.add(processed_content)

            # Create embedding
            embedding = await embeddings_service.create_embedding(
                db=db,
                content_id=question.id,
                content_type=ContentType.TEXT,
                content_text=question.content,
                user_id=user.id,
                metadata={
                    "summary": analysis_result.get("summary", ""),
                    "tags": analysis_result.get("tags", []),
                    "sentiment": analysis_result.get("sentiment", "neutral"),
                },
            )

            db.commit()

            logger.info(f"Successfully processed text question {question.id}")
            return processed_content, embedding

        except Exception as e:
            logger.error(f"Error processing text question {question.id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error processing text: {str(e)}"
            )

    @staticmethod
    async def generate_context_summary(
        db: Session, question: Question, context_images: List[Image], user: User
    ) -> str:
        """Generate a context summary for a question with related images."""
        try:
            context_pieces = []

            # Add question content
            context_pieces.append(f"Question: {question.content}")

            # Add image descriptions
            for image in context_images:
                if image.llm_description:
                    context_pieces.append(f"Image: {image.llm_description}")
                elif image.description:
                    context_pieces.append(f"Image: {image.description}")

            # Generate summary using LLM
            context_text = "\n\n".join(context_pieces)
            summary_result = await llm_service.process_text(context_text, task="summarize")

            summary = summary_result.get("summary", context_text[:300])

            # Update question with context summary
            question.context_summary = summary
            db.commit()

            return summary

        except Exception as e:
            logger.error(f"Error generating context summary: {str(e)}")
            return "Context summary unavailable"

    @staticmethod
    async def batch_process_user_content(
        db: Session, user_id: UUID, content_types: Optional[List[ContentType]] = None
    ) -> Dict[str, int]:
        """Batch process all user content."""
        try:
            results = {"images_processed": 0, "questions_processed": 0, "embeddings_created": 0, "errors": 0}

            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

            # Process images if requested
            if not content_types or ContentType.IMAGE in content_types:
                images = (
                    db.query(Image)
                    .filter(Image.user_id == user_id, Image.processing_status == ProcessingStatus.PENDING)
                    .all()
                )

                for image in images:
                    try:
                        await ContentProcessor.process_image(db, image, user)
                        results["images_processed"] += 1
                        results["embeddings_created"] += 1
                    except Exception as e:
                        logger.error(f"Error processing image {image.id}: {str(e)}")
                        results["errors"] += 1

            # Process questions if requested
            if not content_types or ContentType.TEXT in content_types:
                questions = (
                    db.query(Question)
                    .filter(Question.user_id == user_id, Question.llm_processed_content.is_(None))
                    .all()
                )

                for question in questions:
                    try:
                        if question.type.value == "text":
                            await ContentProcessor.process_text_question(db, question, user)
                        # Audio processing would be handled separately
                        results["questions_processed"] += 1
                        results["embeddings_created"] += 1
                    except Exception as e:
                        logger.error(f"Error processing question {question.id}: {str(e)}")
                        results["errors"] += 1

            return results

        except Exception as e:
            logger.error(f"Error in batch processing: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error in batch processing: {str(e)}",
            )

    @staticmethod
    async def _get_image_content(image: Image) -> bytes:
        """Get image content from storage (placeholder implementation)."""
        # This would need to be implemented based on your storage solution
        # For now, return empty bytes - in real implementation, you'd:
        # 1. Read from local file system using image.file_path
        # 2. Download from Supabase storage using image.url
        # 3. Or read from other storage service

        # Placeholder implementation
        logger.warning(f"Image content retrieval not implemented for {image.id}")
        return b""


# Global instance
content_processor = ContentProcessor()

"""Field service for managing database fields."""

from typing import List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_
from sqlalchemy.orm import Session

from app.models import Database, Field
from app.schemas.field import FieldRequest, FieldUpdateRequest
from app.services.database_service import DatabaseService


class FieldService:
    """Service for managing database fields."""

    def __init__(self, db: Session):
        self.db = db
        self.database_service = DatabaseService(db)

    def create_field(self, database_id: UUID, user_id: UUID, field_data: FieldRequest) -> Optional[Field]:
        """Create a new field in a database."""
        # Validate database access
        if not self.database_service.validate_database_access(database_id, user_id):
            return None

        # Check for duplicate field name
        existing_field = (
            self.db.query(Field)
            .filter(and_(Field.database_id == database_id, Field.name == field_data.name))
            .first()
        )
        if existing_field:
            raise ValueError(f"Field with name '{field_data.name}' already exists")

        # Create field with default settings merged
        field = Field(
            database_id=database_id,
            name=field_data.name,
            type=field_data.type,
            position=field_data.position,
        )

        # Merge default settings with provided settings
        default_settings = field.get_default_settings()
        if field_data.settings:
            default_settings.update(field_data.settings)
        field.settings = default_settings

        self.db.add(field)
        self.db.commit()
        self.db.refresh(field)

        return field

    def get_field(self, field_id: UUID, user_id: UUID) -> Optional[Field]:
        """Get a field by ID with user access validation."""
        return (
            self.db.query(Field)
            .join(Database, Field.database_id == Database.id)
            .filter(and_(Field.id == field_id, Database.owner_id == user_id))
            .first()
        )

    def get_database_fields(self, database_id: UUID, user_id: UUID) -> List[Field]:
        """Get all fields for a database."""
        if not self.database_service.validate_database_access(database_id, user_id):
            return []

        return (
            self.db.query(Field)
            .filter(Field.database_id == database_id)
            .order_by(Field.position, Field.created_at)
            .all()
        )

    def update_field(
        self, field_id: UUID, user_id: UUID, update_data: FieldUpdateRequest
    ) -> Optional[Field]:
        """Update a field."""
        field = self.get_field(field_id, user_id)
        if not field:
            return None

        # Check for duplicate name if name is being updated
        if update_data.name and update_data.name != field.name:
            existing_field = (
                self.db.query(Field)
                .filter(
                    and_(
                        Field.database_id == field.database_id,
                        Field.name == update_data.name,
                        Field.id != field_id,
                    )
                )
                .first()
            )
            if existing_field:
                raise ValueError(f"Field with name '{update_data.name}' already exists")

        # Update field
        update_dict = update_data.dict(exclude_unset=True)
        for field_name, value in update_dict.items():
            if field_name == "settings" and value:
                # Merge with existing settings
                current_settings = field.settings or {}
                current_settings.update(value)
                setattr(field, field_name, current_settings)
            else:
                setattr(field, field_name, value)

        self.db.commit()
        self.db.refresh(field)

        return field

    def delete_field(self, field_id: UUID, user_id: UUID) -> bool:
        """Delete a field and remove it from all records."""
        field = self.get_field(field_id, user_id)
        if not field:
            return False

        # Remove field from all records in the database
        from app.models import Record
        records = (
            self.db.query(Record)
            .filter(Record.database_id == field.database_id)
            .all()
        )

        field_id_str = str(field_id)
        for record in records:
            if record.properties and field_id_str in record.properties:
                del record.properties[field_id_str]
                # Mark as modified for SQLAlchemy to detect the change
                record.properties = record.properties.copy()

        # Delete the field
        self.db.delete(field)
        self.db.commit()

        return True

    def reorder_fields(self, database_id: UUID, user_id: UUID, field_ids: List[UUID]) -> bool:
        """Reorder fields in a database."""
        if not self.database_service.validate_database_access(database_id, user_id):
            return False

        # Get all fields for the database
        fields = self.get_database_fields(database_id, user_id)
        field_map = {field.id: field for field in fields}

        # Validate that all field IDs belong to this database
        for field_id in field_ids:
            if field_id not in field_map:
                raise ValueError(f"Field {field_id} does not belong to this database")

        # Update positions
        for position, field_id in enumerate(field_ids):
            field_map[field_id].position = position

        self.db.commit()
        return True

    def bulk_update_fields(
        self, database_id: UUID, user_id: UUID, field_updates: List[dict]
    ) -> List[Field]:
        """Bulk update multiple fields."""
        if not self.database_service.validate_database_access(database_id, user_id):
            return []

        updated_fields = []
        
        for update in field_updates:
            field_id = update.get("id")
            if not field_id:
                continue

            field = self.get_field(field_id, user_id)
            if not field:
                continue

            # Apply updates
            for key, value in update.items():
                if key != "id" and hasattr(field, key):
                    if key == "settings" and value:
                        current_settings = field.settings or {}
                        current_settings.update(value)
                        setattr(field, key, current_settings)
                    else:
                        setattr(field, key, value)

            updated_fields.append(field)

        self.db.commit()
        return updated_fields

    def validate_field_value(self, field_id: UUID, value, user_id: UUID) -> Tuple[bool, Optional[str]]:
        """Validate a value against a field's constraints."""
        field = self.get_field(field_id, user_id)
        if not field:
            return False, "Field not found"

        return field.validate_value(value)

    def get_field_options(self, field_id: UUID, user_id: UUID) -> List[str]:
        """Get options for select/multi-select fields."""
        field = self.get_field(field_id, user_id)
        if not field or field.type.value not in ["select", "multi_select"]:
            return []

        return field.settings.get("options", [])

    def add_field_option(self, field_id: UUID, user_id: UUID, option: str) -> bool:
        """Add an option to a select/multi-select field."""
        field = self.get_field(field_id, user_id)
        if not field or field.type.value not in ["select", "multi_select"]:
            return False

        settings = field.settings or {}
        options = settings.get("options", [])
        
        if option not in options:
            options.append(option)
            settings["options"] = options
            field.settings = settings
            self.db.commit()

        return True

    def remove_field_option(self, field_id: UUID, user_id: UUID, option: str) -> bool:
        """Remove an option from a select/multi-select field."""
        field = self.get_field(field_id, user_id)
        if not field or field.type.value not in ["select", "multi_select"]:
            return False

        settings = field.settings or {}
        options = settings.get("options", [])
        
        if option in options:
            options.remove(option)
            settings["options"] = options
            field.settings = settings
            
            # Also remove this option from all records
            from app.models import Record
            records = (
                self.db.query(Record)
                .filter(Record.database_id == field.database_id)
                .all()
            )

            field_id_str = str(field_id)
            for record in records:
                if record.properties and field_id_str in record.properties:
                    value = record.properties[field_id_str]
                    if field.type.value == "select" and value == option:
                        record.properties[field_id_str] = None
                    elif field.type.value == "multi_select" and isinstance(value, list):
                        if option in value:
                            value.remove(option)
                            record.properties[field_id_str] = value
                    # Mark as modified
                    record.properties = record.properties.copy()

            self.db.commit()

        return True

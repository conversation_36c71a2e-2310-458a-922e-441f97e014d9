"""Intelligent query service for semantic search and context-aware question answering."""

import logging
from typing import Dict, <PERSON>, Optional, Tuple
from uuid import UUID

from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.orm import Session

from app.models import (
    Answer,
    ContentEmbedding,
    ContentType,
    Image,
    ProcessedContent,
    Question,
    QuestionStatus,
    User,
)
from app.services.embeddings_service import embeddings_service
from app.services.llm_service import llm_service

logger = logging.getLogger(__name__)


class IntelligentQueryService:
    """Service for intelligent question answering with semantic search."""

    @staticmethod
    async def answer_question_with_context(
        db: Session, question: Question, user: User, max_context_items: int = 10
    ) -> Answer:
        """Answer a question using semantic search for relevant context."""
        try:
            # Update question status
            question.status = QuestionStatus.PROCESSING
            db.commit()

            # Get relevant context using semantic search
            context_items = await IntelligentQueryService._get_relevant_context(
                db=db, query_text=question.content, user_id=user.id, max_items=max_context_items
            )

            # Prepare context for LLM
            context_texts = []
            sources = []

            for embedding, similarity in context_items:
                context_texts.append(embedding.content_text)

                # Add source information
                if embedding.content_type == ContentType.IMAGE:
                    sources.append(f"Image analysis (similarity: {similarity:.2f})")
                elif embedding.content_type == ContentType.AUDIO:
                    sources.append(f"Audio transcription (similarity: {similarity:.2f})")
                elif embedding.content_type == ContentType.TEXT:
                    sources.append(f"Text content (similarity: {similarity:.2f})")
                else:
                    sources.append(f"Content (similarity: {similarity:.2f})")

            # Generate answer using LLM with context
            answer_result = await llm_service.answer_question(
                question=question.content, context=context_texts
            )

            # Create answer record
            answer = Answer(
                content=answer_result["answer"],
                confidence=answer_result["confidence"],
                sources=sources,
                llm_model=answer_result.get("model", "gpt-4"),
                reasoning=IntelligentQueryService._generate_reasoning(
                    question.content, context_items, answer_result["answer"]
                ),
                question_id=question.id,
            )

            db.add(answer)

            # Update question status
            question.status = QuestionStatus.COMPLETED

            db.commit()
            db.refresh(answer)

            logger.info(
                f"Successfully answered question {question.id} with {len(context_items)} context items"
            )
            return answer

        except Exception as e:
            logger.error(f"Error answering question {question.id}: {str(e)}")
            question.status = QuestionStatus.FAILED
            db.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error answering question: {str(e)}",
            )

    @staticmethod
    async def semantic_search(
        db: Session,
        query: str,
        user_id: UUID,
        content_types: Optional[List[ContentType]] = None,
        limit: int = 20,
        similarity_threshold: float = 0.7,
    ) -> List[Tuple[ContentEmbedding, float]]:
        """Perform semantic search across user's content."""
        try:
            results = await embeddings_service.search_similar_content(
                db=db,
                query_text=query,
                user_id=user_id,
                content_types=content_types,
                limit=limit,
                similarity_threshold=similarity_threshold,
            )

            logger.info(f"Semantic search for '{query}' returned {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"Error in semantic search: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error in semantic search: {str(e)}",
            )

    @staticmethod
    async def get_content_insights(
        db: Session, user_id: UUID, content_type: Optional[ContentType] = None
    ) -> Dict[str, any]:
        """Get insights about user's content using LLM analysis."""
        try:
            # Get processed content
            query = db.query(ProcessedContent).filter(ProcessedContent.user_id == user_id)

            if content_type:
                query = query.filter(ProcessedContent.content_type == content_type)

            processed_content = query.all()

            if not processed_content:
                return {
                    "total_items": 0,
                    "insights": "No content available for analysis",
                    "common_themes": [],
                    "sentiment_distribution": {},
                    "tag_frequency": {},
                }

            # Aggregate data for analysis
            all_summaries = []
            all_tags = []
            sentiments = []

            for content in processed_content:
                if content.summary:
                    all_summaries.append(content.summary)
                if content.tags:
                    all_tags.extend(content.tags)
                if content.sentiment:
                    sentiments.append(content.sentiment)

            # Generate insights using LLM
            combined_text = "\n".join(all_summaries[:50])  # Limit to avoid token limits

            insights_result = await llm_service.process_text(
                f"Analyze the following content summaries and provide insights about common themes, patterns, and key topics:\n\n{combined_text}",
                task="analyze",
            )

            # Calculate statistics
            tag_frequency = {}
            for tag in all_tags:
                tag_frequency[tag] = tag_frequency.get(tag, 0) + 1

            sentiment_distribution = {}
            for sentiment in sentiments:
                sentiment_distribution[sentiment] = sentiment_distribution.get(sentiment, 0) + 1

            return {
                "total_items": len(processed_content),
                "insights": insights_result.get("summary", "No insights available"),
                "common_themes": insights_result.get("themes", []),
                "sentiment_distribution": sentiment_distribution,
                "tag_frequency": dict(sorted(tag_frequency.items(), key=lambda x: x[1], reverse=True)[:20]),
            }

        except Exception as e:
            logger.error(f"Error getting content insights: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting content insights: {str(e)}",
            )

    @staticmethod
    async def suggest_related_content(
        db: Session, content_id: UUID, content_type: ContentType, user_id: UUID, limit: int = 5
    ) -> List[Tuple[ContentEmbedding, float]]:
        """Suggest content related to a specific item."""
        try:
            # Get the content's embedding
            source_embedding = (
                db.query(ContentEmbedding)
                .filter(
                    ContentEmbedding.content_id == content_id,
                    ContentEmbedding.content_type == content_type,
                    ContentEmbedding.user_id == user_id,
                )
                .first()
            )

            if not source_embedding:
                return []

            # Search for similar content
            similar_content = await embeddings_service.search_similar_content(
                db=db,
                query_text=source_embedding.content_text,
                user_id=user_id,
                limit=limit + 1,  # +1 to exclude the source item
                similarity_threshold=0.5,
            )

            # Filter out the source item
            related_content = [
                (embedding, similarity)
                for embedding, similarity in similar_content
                if embedding.content_id != content_id or embedding.content_type != content_type
            ][:limit]

            return related_content

        except Exception as e:
            logger.error(f"Error suggesting related content: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error suggesting related content: {str(e)}",
            )

    @staticmethod
    async def _get_relevant_context(
        db: Session, query_text: str, user_id: UUID, max_items: int = 10
    ) -> List[Tuple[ContentEmbedding, float]]:
        """Get relevant context for a query using semantic search."""
        try:
            # Search for relevant content
            context_items = await embeddings_service.search_similar_content(
                db=db, query_text=query_text, user_id=user_id, limit=max_items, similarity_threshold=0.6
            )

            return context_items

        except Exception as e:
            logger.error(f"Error getting relevant context: {str(e)}")
            return []

    @staticmethod
    def _generate_reasoning(
        question: str, context_items: List[Tuple[ContentEmbedding, float]], answer: str
    ) -> str:
        """Generate reasoning explanation for the answer."""
        try:
            if not context_items:
                return "Answer generated without specific context from user content."

            context_summary = []
            for embedding, similarity in context_items[:3]:  # Top 3 most relevant
                content_type = embedding.content_type.value
                context_summary.append(f"- {content_type} content (similarity: {similarity:.2f})")

            reasoning = f"""Answer based on the following relevant content:
{chr(10).join(context_summary)}

The response was generated by analyzing the user's question in the context of their uploaded and processed content, focusing on the most semantically similar items."""

            return reasoning

        except Exception as e:
            logger.error(f"Error generating reasoning: {str(e)}")
            return "Reasoning generation failed."

    @staticmethod
    async def get_conversation_context(
        db: Session, user_id: UUID, current_question: str, limit: int = 5
    ) -> List[Dict[str, str]]:
        """Get recent conversation context for better question answering."""
        try:
            # Get recent questions and answers
            recent_questions = (
                db.query(Question)
                .filter(Question.user_id == user_id, Question.status == QuestionStatus.COMPLETED)
                .order_by(Question.created_at.desc())
                .limit(limit)
                .all()
            )

            conversation_history = []
            for question in reversed(recent_questions):  # Reverse to get chronological order
                conversation_history.append({"role": "user", "content": question.content})

                if question.answer:
                    conversation_history.append({"role": "assistant", "content": question.answer.content})

            return conversation_history

        except Exception as e:
            logger.error(f"Error getting conversation context: {str(e)}")
            return []


# Global instance
intelligent_query_service = IntelligentQueryService()

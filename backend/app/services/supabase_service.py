"""Supabase integration service."""

import os
from typing import Any, Dict, List, Optional

from postgrest import APIError
from supabase import Client, create_client

from app.config import settings


class SupabaseService:
    """Service for Supabase integration."""

    def __init__(self):
        self.client: Optional[Client] = None
        if settings.USE_SUPABASE and settings.SUPABASE_URL and settings.SUPABASE_SERVICE_ROLE_KEY:
            self.client = create_client(settings.SUPABASE_URL, settings.SUPABASE_SERVICE_ROLE_KEY)

    def is_available(self) -> bool:
        """Check if Supabase is configured and available."""
        return self.client is not None

    async def get_user_by_id(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID from Supabase Auth."""
        if not self.client:
            return None

        try:
            response = self.client.auth.admin.get_user_by_id(user_id)
            return response.user.dict() if response.user else None
        except Exception:
            return None

    async def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user by email from Supabase Auth."""
        if not self.client:
            return None

        try:
            response = self.client.auth.admin.list_users()
            if response.users:
                for user in response.users:
                    if user.email == email:
                        return user.dict()
            return None
        except Exception:
            return None

    async def create_user(self, email: str, user_metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create user in Supabase Auth."""
        if not self.client:
            return None

        try:
            response = self.client.auth.admin.create_user(
                {"email": email, "user_metadata": user_metadata, "email_confirm": True}
            )
            return response.user.dict() if response.user else None
        except Exception:
            return None

    async def update_user(self, user_id: str, user_metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update user in Supabase Auth."""
        if not self.client:
            return None

        try:
            response = self.client.auth.admin.update_user_by_id(user_id, {"user_metadata": user_metadata})
            return response.user.dict() if response.user else None
        except Exception:
            return None

    async def upload_file(self, bucket: str, file_path: str, file_data: bytes) -> Optional[str]:
        """Upload file to Supabase Storage."""
        if not self.client:
            return None

        try:
            response = self.client.storage.from_(bucket).upload(file_path, file_data)
            if response.get("error"):
                return None

            # Get public URL
            public_url = self.client.storage.from_(bucket).get_public_url(file_path)
            return public_url
        except Exception:
            return None

    async def delete_file(self, bucket: str, file_path: str) -> bool:
        """Delete file from Supabase Storage."""
        if not self.client:
            return False

        try:
            response = self.client.storage.from_(bucket).remove([file_path])
            return not response.get("error")
        except Exception:
            return False

    async def create_bucket(self, bucket_name: str, public: bool = True) -> bool:
        """Create storage bucket."""
        if not self.client:
            return False

        try:
            response = self.client.storage.create_bucket(bucket_name, {"public": public})
            return not response.get("error")
        except Exception:
            return False

    async def list_buckets(self) -> List[Dict[str, Any]]:
        """List storage buckets."""
        if not self.client:
            return []

        try:
            response = self.client.storage.list_buckets()
            return response if isinstance(response, list) else []
        except Exception:
            return []

    def get_database_url(self) -> Optional[str]:
        """Get Supabase PostgreSQL database URL."""
        if not settings.SUPABASE_URL or not settings.SUPABASE_SERVICE_ROLE_KEY:
            return None

        # Extract project ref from Supabase URL
        project_ref = settings.SUPABASE_URL.split("//")[1].split(".")[0]

        # Construct PostgreSQL URL
        # Note: You'll need to get the actual database password from Supabase dashboard
        # This is a template - replace with actual credentials
        return settings.DATABASE_URL

    def get_async_database_url(self) -> Optional[str]:
        """Get async Supabase PostgreSQL database URL."""
        db_url = self.get_database_url()
        if db_url:
            return db_url.replace("postgresql://", "postgresql+asyncpg://")
        return None

    async def execute_sql(self, query: str, params: Optional[Dict] = None) -> Optional[List[Dict]]:
        """Execute raw SQL query."""
        if not self.client:
            return None

        try:
            response = self.client.rpc("execute_sql", {"query": query, "params": params or {}})
            return response.data if hasattr(response, "data") else None
        except Exception:
            return None

    async def setup_database_schema(self) -> bool:
        """Set up database schema for the application."""
        if not self.client:
            return False

        # SQL to create tables (this would be executed via Supabase SQL editor or migration)
        schema_sql = """
        -- Enable UUID extension
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        
        -- Create enum types
        CREATE TYPE oauth_provider AS ENUM ('apple', 'google', 'microsoft');
        CREATE TYPE question_type AS ENUM ('text', 'audio');
        CREATE TYPE question_status AS ENUM ('pending', 'processing', 'completed', 'failed');
        CREATE TYPE token_type AS ENUM ('access', 'refresh');
        
        -- Users table
        CREATE TABLE IF NOT EXISTS users (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            email VARCHAR(255) UNIQUE NOT NULL,
            name VARCHAR(255),
            first_name VARCHAR(255),
            last_name VARCHAR(255),
            avatar_url TEXT,
            provider oauth_provider NOT NULL,
            provider_id VARCHAR(255) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(provider, provider_id)
        );
        
        -- Images table
        CREATE TABLE IF NOT EXISTS images (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            filename VARCHAR(255) NOT NULL,
            original_filename VARCHAR(255) NOT NULL,
            file_path TEXT NOT NULL,
            thumbnail_path TEXT,
            url TEXT NOT NULL,
            thumbnail_url TEXT,
            size INTEGER NOT NULL,
            mime_type VARCHAR(100) NOT NULL,
            width INTEGER,
            height INTEGER,
            description TEXT,
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Questions table
        CREATE TABLE IF NOT EXISTS questions (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            content TEXT NOT NULL,
            type question_type NOT NULL DEFAULT 'text',
            audio_filename VARCHAR(255),
            audio_file_path TEXT,
            audio_url TEXT,
            status question_status NOT NULL DEFAULT 'pending',
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Answers table
        CREATE TABLE IF NOT EXISTS answers (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            content TEXT NOT NULL,
            confidence FLOAT,
            sources TEXT[],
            question_id UUID NOT NULL REFERENCES questions(id) ON DELETE CASCADE UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Auth tokens table
        CREATE TABLE IF NOT EXISTS auth_tokens (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            token TEXT NOT NULL UNIQUE,
            token_type token_type NOT NULL DEFAULT 'access',
            jti VARCHAR(255) NOT NULL UNIQUE,
            is_revoked CHAR(1) NOT NULL DEFAULT '0',
            expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
            user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Junction tables
        CREATE TABLE IF NOT EXISTS question_image_context (
            question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
            image_id UUID REFERENCES images(id) ON DELETE CASCADE,
            PRIMARY KEY (question_id, image_id)
        );
        
        CREATE TABLE IF NOT EXISTS answer_images (
            answer_id UUID REFERENCES answers(id) ON DELETE CASCADE,
            image_id UUID REFERENCES images(id) ON DELETE CASCADE,
            PRIMARY KEY (answer_id, image_id)
        );
        
        -- Indexes
        CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
        CREATE INDEX IF NOT EXISTS idx_users_provider ON users(provider, provider_id);
        CREATE INDEX IF NOT EXISTS idx_images_user_id ON images(user_id);
        CREATE INDEX IF NOT EXISTS idx_questions_user_id ON questions(user_id);
        CREATE INDEX IF NOT EXISTS idx_questions_status ON questions(status);
        CREATE INDEX IF NOT EXISTS idx_auth_tokens_user_id ON auth_tokens(user_id);
        CREATE INDEX IF NOT EXISTS idx_auth_tokens_jti ON auth_tokens(jti);
        
        -- Row Level Security (RLS) policies
        ALTER TABLE users ENABLE ROW LEVEL SECURITY;
        ALTER TABLE images ENABLE ROW LEVEL SECURITY;
        ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE answers ENABLE ROW LEVEL SECURITY;
        ALTER TABLE auth_tokens ENABLE ROW LEVEL SECURITY;
        
        -- RLS Policies (users can only access their own data)
        CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid()::text = id::text);
        CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid()::text = id::text);
        
        CREATE POLICY "Users can view own images" ON images FOR SELECT USING (auth.uid()::text = user_id::text);
        CREATE POLICY "Users can insert own images" ON images FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
        CREATE POLICY "Users can update own images" ON images FOR UPDATE USING (auth.uid()::text = user_id::text);
        CREATE POLICY "Users can delete own images" ON images FOR DELETE USING (auth.uid()::text = user_id::text);
        
        CREATE POLICY "Users can view own questions" ON questions FOR SELECT USING (auth.uid()::text = user_id::text);
        CREATE POLICY "Users can insert own questions" ON questions FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);
        CREATE POLICY "Users can update own questions" ON questions FOR UPDATE USING (auth.uid()::text = user_id::text);
        
        CREATE POLICY "Users can view answers to own questions" ON answers FOR SELECT USING (
            EXISTS (SELECT 1 FROM questions WHERE questions.id = answers.question_id AND auth.uid()::text = questions.user_id::text)
        );
        """

        # Note: This schema would typically be applied via Supabase migrations
        # For now, we'll return True as this is handled via Supabase dashboard
        return True


# Create singleton instance
supabase_service = SupabaseService()

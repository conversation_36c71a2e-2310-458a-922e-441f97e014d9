from langchain_core.vectorstores import InMemoryVectorStore
import getpass
import os
from langchain_community.document_loaders import <PERSON><PERSON>NLoader
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_openai import OpenAIEmbeddings
import dotenv

dotenv.load_dotenv('backend/.env')

cur_dir = 'backend/app/services/ai/'
loader = JSONLoader(
    file_path=cur_dir + "items.jsonl",
    jq_schema=".",
    text_content=False,
json_lines=True,
)

docs = loader.load()
docs = docs[:100]

text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=100)
all_splits = text_splitter.split_documents(docs)

if not os.environ.get("OPENAI_API_KEY"):
  os.environ["OPENAI_API_KEY"] = getpass.getpass("Enter API key for OpenAI: ")


# text-embedding-3-small/large
EMB_MODEL = os.environ('OPENAI_EMBEDDING_MODEL')
embeddings = OpenAIEmbeddings(model=EMB_MODEL)
vector_store = InMemoryVectorStore(embeddings)


# Index chunks
_ = vector_store.add_documents(documents=all_splits)

vector_store.dump(cur_dir+'mock_embedding_100.store')
vector_store2 = InMemoryVectorStore.load(cur_dir+'mock_embedding_100.store', embeddings)

import json
import random

# Define room-specific location fragments
room_locations = {
    "bathroom": [
        "in the closet",
        "on the shelf above the sink",
        "in the medicine cabinet",
        "under the sink"
    ],
    "kitchen": [
        "in the upper cabinet",
        "in the drawer next to the stove",
        "on the countertop near the sink",
        "in the pantry"
    ],
    "living room": [
        "under the couch",
        "on the coffee table",
        "in the side table drawer",
        "on the TV stand"
    ],
    "bedroom": [
        "on the nightstand",
        "in the bedroom closet",
        "under the bed",
        "on the dresser"
    ],
    "laundry room": [
        "in the basket",
        "on the shelf above the washer",
        "in the under-sink cabinet",
        "on the counter"
    ],
    "garage": [
        "on the workbench",
        "on the garage shelf",
        "by the tool rack",
        "in the storage cabinet"
    ],
    "hallway": [
        "on the console table",
        "in the coat closet",
        "on the shoe rack",
        "in the hallway drawer"
    ]
}

# Define possible tags
tags = [
    "personal_sanitary", "haircare", "kitchenware", "cutlery", "appliances",
    "furniture", "electronics", "bedding", "linens", "outerwear",
    "seasonal_clothing", "cleaning_supplies", "housekeeping", "garden_tools",
    "protective_wear", "safety", "medical_supplies", "emergency", "decor", "storage"
]

records = []
for i in range(1, 1001):
    item_id = f"item_{i:04d}"
    room = random.choice(list(room_locations.keys()))
    loc = random.choice(room_locations[room])
    location_full = f"{loc} in the {room}"
    text = f"{item_id} are {location_full}."
    metadata = {
        "item": item_id,
        "location": location_full,
        "tags": random.sample(tags, k=random.choice([1, 2]))
    }
    records.append({
        "id": item_id,
        "text": text,
        "metadata": metadata
    })

# Write to JSONL file
file_path = "/mnt/data/items.jsonl"
with open(file_path, "w") as f:
    for rec in records:
        f.write(json.dumps(rec) + "\n")

# Display first 5 lines as a sanity check
for rec in records[:5]:
    print(json.dumps(rec, ensure_ascii=False))

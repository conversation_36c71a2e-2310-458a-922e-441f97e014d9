import os

from langchain import hub
from langchain_community.document_loaders import <PERSON><PERSON>NLoader
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.graph import START, StateGraph
from typing_extensions import List, TypedDict
from langchain_openai import OpenAIEmbeddings
from langchain_core.vectorstores import InMemoryVectorStore


from langchain.chat_models import init_chat_model
import dotenv
dotenv.load_dotenv('backend/.env')

llm = init_chat_model("gpt-4o-mini", model_provider="openai")

cur_dir = 'backend/app/services/ai/'

# Index chunks
EMB_MODEL = os.environ['OPENAI_EMBEDDING_MODEL']
embeddings = OpenAIEmbeddings(model=EMB_MODEL)
vector_store = InMemoryVectorStore.load(cur_dir+'mock_embedding_100.store', embeddings)

# Define prompt for question-answering
# N.B. for non-US LangSmith endpoints, you may need to specify
# api_url="https://api.smith.langchain.com" in hub.pull.
prompt = hub.pull("rlm/rag-prompt")


# Define state for application
class State(TypedDict):
    question: str
    context: List[Document]
    answer: str


# Define application steps
def retrieve(state: State):
    retrieved_docs = vector_store.similarity_search(state["question"])
    # print('retrieved_docs',retrieved_docs)
    return {"context": retrieved_docs}


def generate(state: State):
    docs_content = "\n\n".join(doc.page_content for doc in state["context"])
    messages = prompt.invoke({"question": state["question"], "context": docs_content})
    response = llm.invoke(messages)
    return {"answer": response.content}


# Compile application and test
graph_builder = StateGraph(State).add_sequence([retrieve, generate])
graph_builder.add_edge(START, "retrieve")
graph = graph_builder.compile()

# Query
# response = graph.invoke({"question": "where is item_0010?"})
response = graph.invoke({"question": "what are in the counter in the laundry room"})

print(response["answer"])

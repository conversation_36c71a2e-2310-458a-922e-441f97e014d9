"""Record service for managing database records."""

import csv
import json
from io import <PERSON><PERSON>
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, cast, func, or_, text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Session, joinedload

from app.models import Database, Field, Record
from app.schemas.record import RecordFilterRequest, RecordRequest, RecordUpdateRequest
from app.services.database_service import DatabaseService


class RecordService:
    """Service for managing database records."""

    def __init__(self, db: Session):
        self.db = db
        self.database_service = DatabaseService(db)

    def create_record(
        self, database_id: UUID, user_id: UUID, record_data: RecordRequest
    ) -> Optional[Record]:
        """Create a new record in a database."""
        # Validate database access
        database = self.database_service.get_database_with_fields(database_id, user_id)
        if not database:
            return None

        # Validate record properties
        record = Record(
            database_id=database_id,
            properties=record_data.properties,
            created_by=user_id,
        )

        is_valid, errors = record.validate_properties(database.fields)
        if not is_valid:
            raise ValueError(f"Validation errors: {'; '.join(errors)}")

        self.db.add(record)
        self.db.commit()
        self.db.refresh(record)

        return record

    def get_record(self, record_id: UUID, user_id: UUID) -> Optional[Record]:
        """Get a record by ID with user access validation."""
        return (
            self.db.query(Record)
            .join(Database, Record.database_id == Database.id)
            .filter(and_(Record.id == record_id, Database.owner_id == user_id))
            .options(joinedload(Record.database).joinedload(Database.fields))
            .first()
        )

    def get_database_records(
        self, database_id: UUID, user_id: UUID, skip: int = 0, limit: int = 20
    ) -> Tuple[List[Record], int]:
        """Get all records for a database with pagination."""
        if not self.database_service.validate_database_access(database_id, user_id):
            return [], 0

        query = self.db.query(Record).filter(Record.database_id == database_id)
        
        total = query.count()
        records = query.offset(skip).limit(limit).all()

        return records, total

    def update_record(
        self, record_id: UUID, user_id: UUID, update_data: RecordUpdateRequest
    ) -> Optional[Record]:
        """Update a record."""
        record = self.get_record(record_id, user_id)
        if not record:
            return None

        # Validate updated properties
        temp_record = Record(
            database_id=record.database_id,
            properties=update_data.properties,
            created_by=record.created_by,
        )

        is_valid, errors = temp_record.validate_properties(record.database.fields)
        if not is_valid:
            raise ValueError(f"Validation errors: {'; '.join(errors)}")

        # Update the record
        record.properties = update_data.properties
        self.db.commit()
        self.db.refresh(record)

        return record

    def delete_record(self, record_id: UUID, user_id: UUID) -> bool:
        """Delete a record."""
        record = self.get_record(record_id, user_id)
        if not record:
            return False

        self.db.delete(record)
        self.db.commit()

        return True

    def filter_records(
        self, database_id: UUID, user_id: UUID, filter_request: RecordFilterRequest
    ) -> Tuple[List[Record], int]:
        """Filter and search records with advanced criteria."""
        if not self.database_service.validate_database_access(database_id, user_id):
            return [], 0

        query = self.db.query(Record).filter(Record.database_id == database_id)

        # Apply filters
        for filter_item in filter_request.filters:
            field_id = filter_item["field_id"]
            operator = filter_item["operator"]
            value = filter_item["value"]

            query = self._apply_filter(query, field_id, operator, value)

        # Apply full-text search
        if filter_request.search:
            search_query = filter_request.search
            query = query.filter(Record.tsv.match(search_query))

        # Apply sorting
        if filter_request.sort_by:
            field_id = filter_request.sort_by
            if filter_request.sort_order == "desc":
                query = query.order_by(
                    cast(Record.properties[field_id], JSONB).desc()
                )
            else:
                query = query.order_by(
                    cast(Record.properties[field_id], JSONB).asc()
                )
        else:
            query = query.order_by(Record.created_at.desc())

        # Get total count before pagination
        total = query.count()

        # Apply pagination
        skip = (filter_request.page - 1) * filter_request.page_size
        records = query.offset(skip).limit(filter_request.page_size).all()

        return records, total

    def _apply_filter(self, query, field_id: str, operator: str, value: Any):
        """Apply a single filter to the query."""
        field_path = Record.properties[field_id]

        if operator == "equals":
            return query.filter(field_path == cast(value, JSONB))
        elif operator == "not_equals":
            return query.filter(field_path != cast(value, JSONB))
        elif operator == "contains":
            if isinstance(value, str):
                return query.filter(
                    cast(field_path, JSONB).astext.ilike(f"%{value}%")
                )
            return query.filter(field_path.contains(value))
        elif operator == "not_contains":
            if isinstance(value, str):
                return query.filter(
                    ~cast(field_path, JSONB).astext.ilike(f"%{value}%")
                )
            return query.filter(~field_path.contains(value))
        elif operator == "starts_with":
            return query.filter(
                cast(field_path, JSONB).astext.ilike(f"{value}%")
            )
        elif operator == "ends_with":
            return query.filter(
                cast(field_path, JSONB).astext.ilike(f"%{value}")
            )
        elif operator == "is_empty":
            return query.filter(
                or_(field_path.is_(None), field_path == cast("", JSONB))
            )
        elif operator == "is_not_empty":
            return query.filter(
                and_(field_path.isnot(None), field_path != cast("", JSONB))
            )
        elif operator == "greater_than":
            return query.filter(
                cast(field_path, JSONB).astext.cast(text("NUMERIC")) > value
            )
        elif operator == "less_than":
            return query.filter(
                cast(field_path, JSONB).astext.cast(text("NUMERIC")) < value
            )
        elif operator == "greater_equal":
            return query.filter(
                cast(field_path, JSONB).astext.cast(text("NUMERIC")) >= value
            )
        elif operator == "less_equal":
            return query.filter(
                cast(field_path, JSONB).astext.cast(text("NUMERIC")) <= value
            )
        elif operator == "in":
            if isinstance(value, list):
                return query.filter(field_path.in_([cast(v, JSONB) for v in value]))
            return query
        elif operator == "not_in":
            if isinstance(value, list):
                return query.filter(~field_path.in_([cast(v, JSONB) for v in value]))
            return query

        return query

    def bulk_create_records(
        self, database_id: UUID, user_id: UUID, records_data: List[RecordRequest]
    ) -> List[Record]:
        """Create multiple records at once."""
        database = self.database_service.get_database_with_fields(database_id, user_id)
        if not database:
            return []

        created_records = []
        errors = []

        for i, record_data in enumerate(records_data):
            try:
                record = Record(
                    database_id=database_id,
                    properties=record_data.properties,
                    created_by=user_id,
                )

                is_valid, validation_errors = record.validate_properties(database.fields)
                if not is_valid:
                    errors.append(f"Record {i + 1}: {'; '.join(validation_errors)}")
                    continue

                self.db.add(record)
                created_records.append(record)

            except Exception as e:
                errors.append(f"Record {i + 1}: {str(e)}")

        if errors:
            self.db.rollback()
            raise ValueError(f"Bulk creation errors: {'; '.join(errors)}")

        self.db.commit()
        return created_records

    def bulk_update_records(
        self, database_id: UUID, user_id: UUID, updates: List[Dict[str, Any]]
    ) -> List[Record]:
        """Update multiple records at once."""
        if not self.database_service.validate_database_access(database_id, user_id):
            return []

        updated_records = []
        
        for update in updates:
            record_id = update.get("id")
            properties = update.get("properties", {})
            
            record = self.get_record(record_id, user_id)
            if record:
                record.properties = properties
                updated_records.append(record)

        self.db.commit()
        return updated_records

    def bulk_delete_records(self, record_ids: List[UUID], user_id: UUID) -> int:
        """Delete multiple records at once."""
        deleted_count = (
            self.db.query(Record)
            .join(Database, Record.database_id == Database.id)
            .filter(
                and_(
                    Record.id.in_(record_ids),
                    Database.owner_id == user_id
                )
            )
            .delete(synchronize_session=False)
        )

        self.db.commit()
        return deleted_count

    def export_records(
        self, database_id: UUID, user_id: UUID, format: str = "csv",
        include_field_names: bool = True, filters: Optional[List[Dict[str, Any]]] = None
    ) -> str:
        """Export records to CSV or JSON format."""
        database = self.database_service.get_database_with_fields(database_id, user_id)
        if not database:
            return ""

        # Get records (apply filters if provided)
        if filters:
            filter_request = RecordFilterRequest(filters=filters, page_size=10000)
            records, _ = self.filter_records(database_id, user_id, filter_request)
        else:
            records, _ = self.get_database_records(database_id, user_id, limit=10000)

        if format == "csv":
            return self._export_to_csv(records, database.fields, include_field_names)
        elif format == "json":
            return self._export_to_json(records, database.fields, include_field_names)
        
        return ""

    def _export_to_csv(self, records: List[Record], fields: List[Field], include_field_names: bool) -> str:
        """Export records to CSV format."""
        output = StringIO()
        
        # Create header
        if include_field_names:
            headers = ["ID"] + [field.name for field in sorted(fields, key=lambda f: f.position)]
        else:
            headers = ["ID"] + [str(field.id) for field in sorted(fields, key=lambda f: f.position)]
        
        writer = csv.writer(output)
        writer.writerow(headers)

        # Write records
        for record in records:
            row = [str(record.id)]
            for field in sorted(fields, key=lambda f: f.position):
                value = record.get_display_value(str(field.id), field)
                row.append(value)
            writer.writerow(row)

        return output.getvalue()

    def _export_to_json(self, records: List[Record], fields: List[Field], include_field_names: bool) -> str:
        """Export records to JSON format."""
        data = []
        
        for record in records:
            record_data = {"id": str(record.id)}
            
            if include_field_names:
                for field in fields:
                    value = record.get_property_value(str(field.id))
                    record_data[field.name] = value
            else:
                record_data["properties"] = record.properties

            data.append(record_data)

        return json.dumps(data, indent=2, default=str)

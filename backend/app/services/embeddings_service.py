"""Embeddings service for vector similarity search and content retrieval."""

import json
import logging
from typing import Dict, List, Optional, Tuple, Union
from uuid import UUID

import numpy as np
from fastapi import HTTPException, status
from sqlalchemy import text
from sqlalchemy.orm import Session

from app.models import ContentEmbedding, ContentType, ProcessedContent
from app.services.llm_service import llm_service

logger = logging.getLogger(__name__)


class EmbeddingsService:
    """Service for managing embeddings and vector similarity search."""

    @staticmethod
    async def create_embedding(
        db: Session,
        content_id: UUID,
        content_type: ContentType,
        content_text: str,
        user_id: UUID,
        metadata: Optional[Dict] = None,
    ) -> ContentEmbedding:
        """Create and store an embedding for content."""
        try:
            # Generate embedding using LLM service
            embedding_vector = await llm_service.generate_embeddings(content_text)

            # Create embedding record
            embedding = ContentEmbedding(
                content_id=content_id,
                content_type=content_type,
                content_text=content_text,
                embedding=embedding_vector,
                content_metadata=metadata or {},
                user_id=user_id,
            )

            db.add(embedding)
            db.commit()
            db.refresh(embedding)

            logger.info(f"Created embedding for {content_type} content {content_id}")
            return embedding

        except Exception as e:
            logger.error(f"Error creating embedding: {str(e)}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating embedding: {str(e)}",
            )

    @staticmethod
    async def update_embedding(
        db: Session, embedding_id: UUID, content_text: str, metadata: Optional[Dict] = None
    ) -> ContentEmbedding:
        """Update an existing embedding."""
        try:
            embedding = db.query(ContentEmbedding).filter(ContentEmbedding.id == embedding_id).first()

            if not embedding:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Embedding not found")

            # Generate new embedding
            embedding_vector = await llm_service.generate_embeddings(content_text)

            # Update embedding
            embedding.content_text = content_text
            embedding.embedding = embedding_vector
            if metadata:
                embedding.content_metadata = metadata

            db.commit()
            db.refresh(embedding)

            return embedding

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating embedding: {str(e)}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error updating embedding: {str(e)}",
            )

    @staticmethod
    async def search_similar_content(
        db: Session,
        query_text: str,
        user_id: UUID,
        content_types: Optional[List[ContentType]] = None,
        limit: int = 10,
        similarity_threshold: float = 0.7,
    ) -> List[Tuple[ContentEmbedding, float]]:
        """Search for similar content using vector similarity."""
        try:
            # Generate embedding for query
            query_embedding = await llm_service.generate_embeddings(query_text)

            # Build SQL query for vector similarity search
            query_sql = """
            SELECT
                id, content_id, content_type, content_text, metadata, user_id, created_at, updated_at,
                1 - (embedding <=> :query_embedding) as similarity
            FROM content_embeddings
            WHERE user_id = :user_id
            """

            params = {"query_embedding": str(query_embedding), "user_id": str(user_id)}

            # Add content type filter if specified
            if content_types:
                placeholders = ",".join([f":content_type_{i}" for i in range(len(content_types))])
                query_sql += f" AND content_type IN ({placeholders})"
                for i, content_type in enumerate(content_types):
                    params[f"content_type_{i}"] = content_type.value

            # Add similarity threshold and ordering
            query_sql += """
            AND (1 - (embedding <=> :query_embedding)) >= :similarity_threshold
            ORDER BY embedding <=> :query_embedding
            LIMIT :limit
            """

            params["similarity_threshold"] = similarity_threshold
            params["limit"] = limit

            # Execute query
            result = db.execute(text(query_sql), params)
            rows = result.fetchall()

            # Convert results to ContentEmbedding objects with similarity scores
            results = []
            for row in rows:
                embedding = ContentEmbedding(
                    id=row.id,
                    content_id=row.content_id,
                    content_type=ContentType(row.content_type),
                    content_text=row.content_text,
                    content_metadata=row.metadata,
                    user_id=row.user_id,
                    created_at=row.created_at,
                    updated_at=row.updated_at,
                )
                results.append((embedding, row.similarity))

            return results

        except Exception as e:
            logger.error(f"Error searching similar content: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error searching similar content: {str(e)}",
            )

    @staticmethod
    async def get_content_context(
        db: Session, query_text: str, user_id: UUID, max_context_length: int = 4000
    ) -> List[str]:
        """Get relevant content context for a query."""
        try:
            # Search for similar content
            similar_content = await EmbeddingsService.search_similar_content(
                db=db, query_text=query_text, user_id=user_id, limit=20, similarity_threshold=0.6
            )

            # Build context from similar content
            context_pieces = []
            total_length = 0

            for embedding, similarity in similar_content:
                content_text = embedding.content_text

                # Add metadata context if available
                if embedding.content_metadata:
                    metadata_str = json.dumps(embedding.content_metadata, indent=2)
                    content_text = f"{content_text}\n\nMetadata: {metadata_str}"

                # Check if adding this content would exceed limit
                if total_length + len(content_text) > max_context_length:
                    break

                context_pieces.append(content_text)
                total_length += len(content_text)

            return context_pieces

        except Exception as e:
            logger.error(f"Error getting content context: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting content context: {str(e)}",
            )

    @staticmethod
    async def batch_create_embeddings(
        db: Session, content_items: List[Dict], user_id: UUID
    ) -> List[ContentEmbedding]:
        """Create embeddings for multiple content items."""
        try:
            embeddings = []

            for item in content_items:
                embedding = await EmbeddingsService.create_embedding(
                    db=db,
                    content_id=item["content_id"],
                    content_type=item["content_type"],
                    content_text=item["content_text"],
                    user_id=user_id,
                    metadata=item.get("metadata"),
                )
                embeddings.append(embedding)

            return embeddings

        except Exception as e:
            logger.error(f"Error in batch creating embeddings: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error in batch creating embeddings: {str(e)}",
            )

    @staticmethod
    def delete_embeddings_by_content(db: Session, content_id: UUID, content_type: ContentType) -> int:
        """Delete embeddings for specific content."""
        try:
            deleted_count = (
                db.query(ContentEmbedding)
                .filter(
                    ContentEmbedding.content_id == content_id, ContentEmbedding.content_type == content_type
                )
                .delete()
            )

            db.commit()

            logger.info(f"Deleted {deleted_count} embeddings for {content_type} content {content_id}")
            return deleted_count

        except Exception as e:
            logger.error(f"Error deleting embeddings: {str(e)}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting embeddings: {str(e)}",
            )

    @staticmethod
    async def reindex_user_content(db: Session, user_id: UUID) -> Dict[str, int]:
        """Reindex all content for a user."""
        try:
            # Get all processed content for user
            processed_content = db.query(ProcessedContent).filter(ProcessedContent.user_id == user_id).all()

            # Delete existing embeddings
            db.query(ContentEmbedding).filter(ContentEmbedding.user_id == user_id).delete()

            # Create new embeddings
            created_count = 0
            for content in processed_content:
                await EmbeddingsService.create_embedding(
                    db=db,
                    content_id=content.content_id,
                    content_type=content.content_type,
                    content_text=content.processed_content,
                    user_id=user_id,
                    metadata={
                        "summary": content.summary,
                        "tags": content.tags,
                        "sentiment": content.sentiment,
                    },
                )
                created_count += 1

            return {"reindexed_count": created_count, "user_id": str(user_id)}

        except Exception as e:
            logger.error(f"Error reindexing user content: {str(e)}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error reindexing user content: {str(e)}",
            )


# Global instance
embeddings_service = EmbeddingsService()

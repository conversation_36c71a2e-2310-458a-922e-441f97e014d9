"""Authentication API routes."""

from datetime import <PERSON><PERSON><PERSON>

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.auth import (
    J<PERSON><PERSON><PERSON><PERSON>,
    apple_oauth,
    get_current_user,
    google_oauth,
    microsoft_oauth,
)
from app.config import settings
from app.database import get_db
from app.models import AuthToken, OAuthProvider, TokenType, User
from app.schemas import (
    AppleAuthRequest,
    AuthResponse,
    GoogleAuthRequest,
    MessageResponse,
    MicrosoftAuthRequest,
    RefreshTokenRequest,
    TokenResponse,
    UserProfile,
)

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/oauth/apple", response_model=AuthResponse)
async def apple_signin(auth_request: AppleAuthRequest, db: Session = Depends(get_db)):
    """Authenticate user with Apple Sign-In."""
    # Verify Apple token
    user_info = await apple_oauth.verify_token(auth_request.dict())
    if not user_info:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid Apple credentials")

    # Check if user exists
    user = (
        db.query(User)
        .filter(User.provider == OAuthProvider.APPLE, User.provider_id == user_info["provider_id"])
        .first()
    )

    if not user:
        # Create new user
        user = User(
            email=user_info["email"],
            name=user_info.get("name"),
            first_name=user_info.get("first_name"),
            last_name=user_info.get("last_name"),
            provider=OAuthProvider.APPLE,
            provider_id=user_info["provider_id"],
        )
        db.add(user)
        db.commit()
        db.refresh(user)
    else:
        # Update existing user info if provided
        if user_info.get("name"):
            user.name = user_info["name"]
        if user_info.get("first_name"):
            user.first_name = user_info["first_name"]
        if user_info.get("last_name"):
            user.last_name = user_info["last_name"]
        db.commit()

    # Create tokens
    access_token_data = JWTHandler.create_access_token(str(user.id))
    refresh_token_data = JWTHandler.create_refresh_token(str(user.id))

    # Store tokens in database
    JWTHandler.store_token(db, str(user.id), access_token_data, TokenType.ACCESS)
    JWTHandler.store_token(db, str(user.id), refresh_token_data, TokenType.REFRESH)

    return AuthResponse(
        access_token=access_token_data["token"],
        refresh_token=refresh_token_data["token"],
        token_type="bearer",
        expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=UserProfile.from_orm(user),
    )


@router.post("/oauth/google", response_model=AuthResponse)
async def google_signin(auth_request: GoogleAuthRequest, db: Session = Depends(get_db)):
    """Authenticate user with Google Sign-In."""
    # Verify Google token
    user_info = await google_oauth.verify_token(auth_request.dict())
    if not user_info:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid Google credentials")

    # Check if user exists
    user = (
        db.query(User)
        .filter(User.provider == OAuthProvider.GOOGLE, User.provider_id == user_info["provider_id"])
        .first()
    )

    if not user:
        # Create new user
        user = User(
            email=user_info["email"],
            name=user_info.get("name"),
            first_name=user_info.get("first_name"),
            last_name=user_info.get("last_name"),
            avatar_url=user_info.get("avatar_url"),
            provider=OAuthProvider.GOOGLE,
            provider_id=user_info["provider_id"],
        )
        db.add(user)
        db.commit()
        db.refresh(user)
    else:
        # Update existing user info
        user.name = user_info.get("name") or user.name
        user.first_name = user_info.get("first_name") or user.first_name
        user.last_name = user_info.get("last_name") or user.last_name
        user.avatar_url = user_info.get("avatar_url") or user.avatar_url
        db.commit()

    # Create tokens
    access_token_data = JWTHandler.create_access_token(str(user.id))
    refresh_token_data = JWTHandler.create_refresh_token(str(user.id))

    # Store tokens in database
    JWTHandler.store_token(db, str(user.id), access_token_data, TokenType.ACCESS)
    JWTHandler.store_token(db, str(user.id), refresh_token_data, TokenType.REFRESH)

    return AuthResponse(
        access_token=access_token_data["token"],
        refresh_token=refresh_token_data["token"],
        token_type="bearer",
        expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=UserProfile.from_orm(user),
    )


@router.post("/oauth/microsoft", response_model=AuthResponse)
async def microsoft_signin(auth_request: MicrosoftAuthRequest, db: Session = Depends(get_db)):
    """Authenticate user with Microsoft Sign-In."""
    # Verify Microsoft token
    user_info = await microsoft_oauth.verify_token(auth_request.dict())
    if not user_info:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid Microsoft credentials")

    # Check if user exists
    user = (
        db.query(User)
        .filter(User.provider == OAuthProvider.MICROSOFT, User.provider_id == user_info["provider_id"])
        .first()
    )

    if not user:
        # Create new user
        user = User(
            email=user_info["email"],
            name=user_info.get("name"),
            first_name=user_info.get("first_name"),
            last_name=user_info.get("last_name"),
            provider=OAuthProvider.MICROSOFT,
            provider_id=user_info["provider_id"],
        )
        db.add(user)
        db.commit()
        db.refresh(user)
    else:
        # Update existing user info
        user.name = user_info.get("name") or user.name
        user.first_name = user_info.get("first_name") or user.first_name
        user.last_name = user_info.get("last_name") or user.last_name
        db.commit()

    # Create tokens
    access_token_data = JWTHandler.create_access_token(str(user.id))
    refresh_token_data = JWTHandler.create_refresh_token(str(user.id))

    # Store tokens in database
    JWTHandler.store_token(db, str(user.id), access_token_data, TokenType.ACCESS)
    JWTHandler.store_token(db, str(user.id), refresh_token_data, TokenType.REFRESH)

    return AuthResponse(
        access_token=access_token_data["token"],
        refresh_token=refresh_token_data["token"],
        token_type="bearer",
        expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        user=UserProfile.from_orm(user),
    )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(refresh_request: RefreshTokenRequest, db: Session = Depends(get_db)):
    """Refresh access token using refresh token."""
    # Verify refresh token
    payload = JWTHandler.verify_token(refresh_request.refresh_token)
    if not payload or payload.get("type") != "refresh":
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid refresh token")

    # Check if token is revoked
    jti = payload.get("jti")
    if jti and JWTHandler.is_token_revoked(db, jti):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Refresh token has been revoked")

    # Get user
    user_id = payload.get("sub")
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="User not found")

    # Create new access token
    access_token_data = JWTHandler.create_access_token(str(user.id))

    # Store new token in database
    JWTHandler.store_token(db, str(user.id), access_token_data, TokenType.ACCESS)

    return TokenResponse(
        access_token=access_token_data["token"],
        token_type="bearer",
        expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
    )


@router.post("/logout", response_model=MessageResponse)
async def logout(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)):
    """Logout user and revoke all tokens."""
    # Revoke all user tokens
    revoked_count = JWTHandler.revoke_all_user_tokens(db, str(current_user.id))

    return MessageResponse(message=f"Successfully logged out. {revoked_count} tokens revoked.")

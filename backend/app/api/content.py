"""Content processing API routes for LLM-powered analysis."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from app.auth import get_current_user
from app.database import get_db
from app.models import ContentType, Image, ProcessedContent, ProcessingStatus, Question, User
from app.services.content_processor import content_processor
from app.services.embeddings_service import embeddings_service

router = APIRouter(prefix="/content", tags=["Content Processing"])


class ProcessContentRequest(BaseModel):
    """Process content request schema."""

    content_ids: List[UUID] = Field(..., description="List of content IDs to process")
    content_type: ContentType = Field(..., description="Type of content to process")
    force_reprocess: bool = Field(default=False, description="Force reprocessing even if already processed")


class ProcessedContentResponse(BaseModel):
    """Processed content response schema."""

    id: UUID = Field(..., description="Processed content unique identifier")
    content_id: UUID = Field(..., description="Original content ID")
    content_type: str = Field(..., description="Type of content")
    summary: Optional[str] = Field(None, description="Content summary")
    key_points: List[str] = Field(default=[], description="Key points extracted")
    tags: List[str] = Field(default=[], description="Content tags")
    sentiment: Optional[str] = Field(None, description="Sentiment analysis result")
    confidence: Optional[float] = Field(None, description="Processing confidence score")
    llm_model: Optional[str] = Field(None, description="LLM model used for processing")
    created_at: str = Field(..., description="Processing timestamp")


class BatchProcessResponse(BaseModel):
    """Batch processing response schema."""

    processed_count: int = Field(..., description="Number of items processed")
    failed_count: int = Field(..., description="Number of items that failed processing")
    processing_ids: List[UUID] = Field(..., description="IDs of items being processed")


class EmbeddingResponse(BaseModel):
    """Embedding response schema."""

    id: UUID = Field(..., description="Embedding unique identifier")
    content_id: UUID = Field(..., description="Original content ID")
    content_type: str = Field(..., description="Type of content")
    content_text: str = Field(..., description="Text used for embedding")
    metadata: dict = Field(default={}, description="Additional metadata")
    created_at: str = Field(..., description="Embedding creation timestamp")


@router.post("/process", response_model=BatchProcessResponse)
async def process_content(
    request: ProcessContentRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Process content with LLM analysis and generate embeddings."""
    try:
        processed_count = 0
        failed_count = 0
        processing_ids = []

        for content_id in request.content_ids:
            try:
                if request.content_type == ContentType.IMAGE:
                    # Get image
                    image = (
                        db.query(Image)
                        .filter(Image.id == content_id, Image.user_id == current_user.id)
                        .first()
                    )

                    if not image:
                        failed_count += 1
                        continue

                    # Check if already processed and not forcing reprocess
                    if image.processing_status == ProcessingStatus.COMPLETED and not request.force_reprocess:
                        continue

                    # Process in background
                    background_tasks.add_task(content_processor.process_image, db, image, current_user)
                    processing_ids.append(content_id)
                    processed_count += 1

                elif request.content_type == ContentType.TEXT:
                    # Get question
                    question = (
                        db.query(Question)
                        .filter(Question.id == content_id, Question.user_id == current_user.id)
                        .first()
                    )

                    if not question:
                        failed_count += 1
                        continue

                    # Check if already processed
                    if question.llm_processed_content and not request.force_reprocess:
                        continue

                    # Process in background
                    background_tasks.add_task(
                        content_processor.process_text_question, db, question, current_user
                    )
                    processing_ids.append(content_id)
                    processed_count += 1

            except Exception as e:
                failed_count += 1
                continue

        return BatchProcessResponse(
            processed_count=processed_count, failed_count=failed_count, processing_ids=processing_ids
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error processing content: {str(e)}"
        )


@router.get("/processed", response_model=List[ProcessedContentResponse])
async def get_processed_content(
    content_type: Optional[ContentType] = Query(None, description="Filter by content type"),
    limit: int = Query(default=50, ge=1, le=200, description="Maximum number of results"),
    offset: int = Query(default=0, ge=0, description="Number of results to skip"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get user's processed content."""
    try:
        query = db.query(ProcessedContent).filter(ProcessedContent.user_id == current_user.id)

        if content_type:
            query = query.filter(ProcessedContent.content_type == content_type)

        processed_content = (
            query.order_by(ProcessedContent.created_at.desc()).offset(offset).limit(limit).all()
        )

        results = []
        for content in processed_content:
            result = ProcessedContentResponse(
                id=content.id,
                content_id=content.content_id,
                content_type=content.content_type.value,
                summary=content.summary,
                key_points=content.key_points or [],
                tags=content.tags or [],
                sentiment=content.sentiment,
                confidence=content.confidence,
                llm_model=content.llm_model,
                created_at=content.created_at.isoformat(),
            )
            results.append(result)

        return results

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting processed content: {str(e)}",
        )


@router.get("/embeddings", response_model=List[EmbeddingResponse])
async def get_embeddings(
    content_type: Optional[ContentType] = Query(None, description="Filter by content type"),
    limit: int = Query(default=50, ge=1, le=200, description="Maximum number of results"),
    offset: int = Query(default=0, ge=0, description="Number of results to skip"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get user's content embeddings."""
    try:
        from app.models import ContentEmbedding

        query = db.query(ContentEmbedding).filter(ContentEmbedding.user_id == current_user.id)

        if content_type:
            query = query.filter(ContentEmbedding.content_type == content_type)

        embeddings = query.order_by(ContentEmbedding.created_at.desc()).offset(offset).limit(limit).all()

        results = []
        for embedding in embeddings:
            result = EmbeddingResponse(
                id=embedding.id,
                content_id=embedding.content_id,
                content_type=embedding.content_type.value,
                content_text=embedding.content_text,
                metadata=embedding.content_metadata or {},
                created_at=embedding.created_at.isoformat(),
            )
            results.append(result)

        return results

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error getting embeddings: {str(e)}"
        )


@router.post("/reindex")
async def reindex_user_content(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Reindex all user content for embeddings."""
    try:
        # Reindex in background
        background_tasks.add_task(embeddings_service.reindex_user_content, db, current_user.id)

        return {"message": "Content reindexing started", "user_id": str(current_user.id)}

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error starting reindex: {str(e)}"
        )

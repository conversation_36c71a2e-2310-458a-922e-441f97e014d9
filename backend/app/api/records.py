"""Record API endpoints."""

from typing import List
from uuid import UUID

import structlog
from fastapi import APIRouter, Depends, HTTPException, Response, status
from sqlalchemy.orm import Session

from app.auth.dependencies import get_current_user
from app.database import get_db
from app.models import User
from app.schemas import (
    MessageResponse,
    RecordBulkCreateRequest,
    RecordBulkDeleteRequest,
    RecordBulkUpdateRequest,
    RecordDetailResponse,
    RecordExportRequest,
    RecordFilterRequest,
    RecordListResponse,
    RecordRequest,
    RecordResponse,
    RecordUpdateRequest,
    RecordValidationResponse,
)
from app.services.record_service import RecordService

logger = structlog.get_logger()

router = APIRouter(prefix="/databases/{database_id}/records", tags=["records"])


@router.post("/", response_model=RecordResponse, status_code=status.HTTP_201_CREATED)
async def create_record(
    database_id: UUID,
    record_data: RecordRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Create a new record in a database."""
    try:
        service = RecordService(db)
        record = service.create_record(database_id, current_user.id, record_data)
        
        if not record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Database not found"
            )
        
        logger.info(
            "Record created",
            record_id=str(record.id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        
        return RecordResponse.from_orm(record)
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            "Failed to create record",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create record"
        )


@router.get("/", response_model=RecordListResponse)
async def get_records(
    database_id: UUID,
    skip: int = 0,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get all records for a database with pagination."""
    try:
        service = RecordService(db)
        records, total = service.get_database_records(database_id, current_user.id, skip, limit)
        
        return RecordListResponse(
            records=[RecordResponse.from_orm(record) for record in records],
            pagination={
                "page": skip // limit + 1,
                "page_size": limit,
                "total_items": total,
                "total_pages": (total + limit - 1) // limit,
            },
        )
    
    except Exception as e:
        logger.error(
            "Failed to get records",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve records"
        )


@router.post("/filter", response_model=RecordListResponse)
async def filter_records(
    database_id: UUID,
    filter_request: RecordFilterRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Filter and search records with advanced criteria."""
    try:
        service = RecordService(db)
        records, total = service.filter_records(database_id, current_user.id, filter_request)
        
        return RecordListResponse(
            records=[RecordResponse.from_orm(record) for record in records],
            pagination={
                "page": filter_request.page,
                "page_size": filter_request.page_size,
                "total_items": total,
                "total_pages": (total + filter_request.page_size - 1) // filter_request.page_size,
            },
        )
    
    except Exception as e:
        logger.error(
            "Failed to filter records",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to filter records"
        )


@router.get("/{record_id}", response_model=RecordDetailResponse)
async def get_record(
    database_id: UUID,
    record_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get a specific record with field names."""
    try:
        service = RecordService(db)
        record = service.get_record(record_id, current_user.id)
        
        if not record or record.database_id != database_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Record not found"
            )
        
        # Convert to detailed response with field names
        response_data = record.to_dict(include_field_names=True)
        return RecordDetailResponse(**response_data)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get record",
            error=str(e),
            record_id=str(record_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve record"
        )


@router.put("/{record_id}", response_model=RecordResponse)
async def update_record(
    database_id: UUID,
    record_id: UUID,
    update_data: RecordUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Update a record."""
    try:
        service = RecordService(db)
        record = service.update_record(record_id, current_user.id, update_data)
        
        if not record or record.database_id != database_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Record not found"
            )
        
        logger.info(
            "Record updated",
            record_id=str(record_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        
        return RecordResponse.from_orm(record)
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to update record",
            error=str(e),
            record_id=str(record_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update record"
        )


@router.delete("/{record_id}", response_model=MessageResponse)
async def delete_record(
    database_id: UUID,
    record_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Delete a record."""
    try:
        service = RecordService(db)
        success = service.delete_record(record_id, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Record not found"
            )
        
        logger.info(
            "Record deleted",
            record_id=str(record_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        
        return MessageResponse(message="Record deleted successfully")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to delete record",
            error=str(e),
            record_id=str(record_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete record"
        )


@router.post("/bulk", response_model=List[RecordResponse])
async def bulk_create_records(
    database_id: UUID,
    bulk_data: RecordBulkCreateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Create multiple records at once."""
    try:
        service = RecordService(db)
        records = service.bulk_create_records(database_id, current_user.id, bulk_data.records)
        
        logger.info(
            "Records bulk created",
            database_id=str(database_id),
            user_id=str(current_user.id),
            record_count=len(records),
        )
        
        return [RecordResponse.from_orm(record) for record in records]
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            "Failed to bulk create records",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bulk create records"
        )


@router.put("/bulk", response_model=List[RecordResponse])
async def bulk_update_records(
    database_id: UUID,
    bulk_data: RecordBulkUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Update multiple records at once."""
    try:
        service = RecordService(db)
        records = service.bulk_update_records(database_id, current_user.id, bulk_data.updates)
        
        logger.info(
            "Records bulk updated",
            database_id=str(database_id),
            user_id=str(current_user.id),
            record_count=len(records),
        )
        
        return [RecordResponse.from_orm(record) for record in records]
    
    except Exception as e:
        logger.error(
            "Failed to bulk update records",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bulk update records"
        )


@router.delete("/bulk", response_model=MessageResponse)
async def bulk_delete_records(
    database_id: UUID,
    bulk_data: RecordBulkDeleteRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Delete multiple records at once."""
    try:
        service = RecordService(db)
        deleted_count = service.bulk_delete_records(bulk_data.record_ids, current_user.id)
        
        logger.info(
            "Records bulk deleted",
            database_id=str(database_id),
            user_id=str(current_user.id),
            deleted_count=deleted_count,
        )
        
        return MessageResponse(message=f"Successfully deleted {deleted_count} records")
    
    except Exception as e:
        logger.error(
            "Failed to bulk delete records",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bulk delete records"
        )


@router.post("/export")
async def export_records(
    database_id: UUID,
    export_request: RecordExportRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Export records to CSV or JSON format."""
    try:
        service = RecordService(db)
        export_data = service.export_records(
            database_id,
            current_user.id,
            export_request.format,
            export_request.include_field_names,
            export_request.filters,
        )
        
        if not export_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Database not found or no records to export"
            )
        
        # Set appropriate content type and filename
        if export_request.format == "csv":
            media_type = "text/csv"
            filename = f"database_{database_id}_export.csv"
        else:
            media_type = "application/json"
            filename = f"database_{database_id}_export.json"
        
        logger.info(
            "Records exported",
            database_id=str(database_id),
            user_id=str(current_user.id),
            format=export_request.format,
        )
        
        return Response(
            content=export_data,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to export records",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export records"
        )

"""Field API endpoints."""

from typing import List
from uuid import UUID

import structlog
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.auth.dependencies import get_current_user
from app.database import get_db
from app.models import User
from app.schemas import (
    FieldBulkUpdateRequest,
    FieldReorderRequest,
    FieldRequest,
    FieldResponse,
    FieldUpdateRequest,
    FieldValidationResponse,
    MessageResponse,
)
from app.services.field_service import FieldService

logger = structlog.get_logger()

router = APIRouter(prefix="/databases/{database_id}/fields", tags=["fields"])


@router.post("/", response_model=FieldResponse, status_code=status.HTTP_201_CREATED)
async def create_field(
    database_id: UUID,
    field_data: FieldRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Create a new field in a database."""
    try:
        service = FieldService(db)
        field = service.create_field(database_id, current_user.id, field_data)
        
        if not field:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Database not found"
            )
        
        logger.info(
            "Field created",
            field_id=str(field.id),
            database_id=str(database_id),
            user_id=str(current_user.id),
            name=field.name,
            type=field.type.value,
        )
        
        return FieldResponse.from_orm(field)
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(
            "Failed to create field",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create field"
        )


@router.get("/", response_model=List[FieldResponse])
async def get_fields(
    database_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get all fields for a database."""
    try:
        service = FieldService(db)
        fields = service.get_database_fields(database_id, current_user.id)
        
        return [FieldResponse.from_orm(field) for field in fields]
    
    except Exception as e:
        logger.error(
            "Failed to get fields",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve fields"
        )


@router.get("/{field_id}", response_model=FieldResponse)
async def get_field(
    database_id: UUID,
    field_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get a specific field."""
    try:
        service = FieldService(db)
        field = service.get_field(field_id, current_user.id)
        
        if not field or field.database_id != database_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Field not found"
            )
        
        return FieldResponse.from_orm(field)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get field",
            error=str(e),
            field_id=str(field_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve field"
        )


@router.put("/{field_id}", response_model=FieldResponse)
async def update_field(
    database_id: UUID,
    field_id: UUID,
    update_data: FieldUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Update a field."""
    try:
        service = FieldService(db)
        field = service.update_field(field_id, current_user.id, update_data)
        
        if not field or field.database_id != database_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Field not found"
            )
        
        logger.info(
            "Field updated",
            field_id=str(field_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        
        return FieldResponse.from_orm(field)
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to update field",
            error=str(e),
            field_id=str(field_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update field"
        )


@router.delete("/{field_id}", response_model=MessageResponse)
async def delete_field(
    database_id: UUID,
    field_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Delete a field."""
    try:
        service = FieldService(db)
        success = service.delete_field(field_id, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Field not found"
            )
        
        logger.info(
            "Field deleted",
            field_id=str(field_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        
        return MessageResponse(message="Field deleted successfully")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to delete field",
            error=str(e),
            field_id=str(field_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete field"
        )


@router.post("/reorder", response_model=MessageResponse)
async def reorder_fields(
    database_id: UUID,
    reorder_data: FieldReorderRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Reorder fields in a database."""
    try:
        service = FieldService(db)
        success = service.reorder_fields(database_id, current_user.id, reorder_data.field_ids)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Database not found"
            )
        
        logger.info(
            "Fields reordered",
            database_id=str(database_id),
            user_id=str(current_user.id),
            field_count=len(reorder_data.field_ids),
        )
        
        return MessageResponse(message="Fields reordered successfully")
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to reorder fields",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reorder fields"
        )


@router.put("/bulk", response_model=List[FieldResponse])
async def bulk_update_fields(
    database_id: UUID,
    bulk_data: FieldBulkUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Bulk update multiple fields."""
    try:
        service = FieldService(db)
        fields = service.bulk_update_fields(database_id, current_user.id, bulk_data.fields)
        
        logger.info(
            "Fields bulk updated",
            database_id=str(database_id),
            user_id=str(current_user.id),
            field_count=len(fields),
        )
        
        return [FieldResponse.from_orm(field) for field in fields]
    
    except Exception as e:
        logger.error(
            "Failed to bulk update fields",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to bulk update fields"
        )


@router.post("/{field_id}/validate", response_model=FieldValidationResponse)
async def validate_field_value(
    database_id: UUID,
    field_id: UUID,
    value: dict,  # {"value": <actual_value>}
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Validate a value against a field's constraints."""
    try:
        service = FieldService(db)
        is_valid, error = service.validate_field_value(
            field_id, value.get("value"), current_user.id
        )
        
        return FieldValidationResponse(
            is_valid=is_valid,
            error=error,
            formatted_value=value.get("value") if is_valid else None,
        )
    
    except Exception as e:
        logger.error(
            "Failed to validate field value",
            error=str(e),
            field_id=str(field_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate field value"
        )


@router.get("/{field_id}/options")
async def get_field_options(
    database_id: UUID,
    field_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get options for select/multi-select fields."""
    try:
        service = FieldService(db)
        options = service.get_field_options(field_id, current_user.id)
        
        return {"options": options}
    
    except Exception as e:
        logger.error(
            "Failed to get field options",
            error=str(e),
            field_id=str(field_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve field options"
        )


@router.post("/{field_id}/options", response_model=MessageResponse)
async def add_field_option(
    database_id: UUID,
    field_id: UUID,
    option_data: dict,  # {"option": "new_option"}
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Add an option to a select/multi-select field."""
    try:
        service = FieldService(db)
        success = service.add_field_option(field_id, current_user.id, option_data.get("option", ""))
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot add option to this field type"
            )
        
        return MessageResponse(message="Option added successfully")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to add field option",
            error=str(e),
            field_id=str(field_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add field option"
        )


@router.delete("/{field_id}/options/{option}", response_model=MessageResponse)
async def remove_field_option(
    database_id: UUID,
    field_id: UUID,
    option: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Remove an option from a select/multi-select field."""
    try:
        service = FieldService(db)
        success = service.remove_field_option(field_id, current_user.id, option)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove option from this field type"
            )
        
        return MessageResponse(message="Option removed successfully")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to remove field option",
            error=str(e),
            field_id=str(field_id),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove field option"
        )

"""Question API routes."""

import math
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, BackgroundTasks, Depends, File, Form, HTTPException, Query, UploadFile, status
from sqlalchemy.orm import Session

from app.auth import get_current_user
from app.database import get_db
from app.models import Image, Question, QuestionType, User
from app.schemas import (
    PaginationInfo,
    QuestionDetailResponse,
    QuestionListResponse,
    QuestionRequest,
    QuestionResponse,
)
from app.services import QuestionService

router = APIRouter(prefix="/questions", tags=["Questions"])


@router.post("", response_model=QuestionResponse, status_code=status.HTTP_201_CREATED)
async def submit_text_question(
    question_request: QuestionRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Submit a text question."""
    # Get context images if provided
    context_images = []
    if question_request.context_images:
        context_images = (
            db.query(Image)
            .filter(Image.id.in_(question_request.context_images), Image.user_id == current_user.id)
            .all()
        )

    # Create question
    question = Question(content=question_request.content, type=QuestionType.TEXT, user_id=current_user.id)

    # Add context images
    if context_images:
        question.context_images = context_images

    db.add(question)
    db.commit()
    db.refresh(question)

    # Generate answer in background
    background_tasks.add_task(QuestionService.generate_answer, question, context_images, db)

    return QuestionResponse.from_orm(question)


@router.post("/audio", response_model=QuestionResponse, status_code=status.HTTP_201_CREATED)
async def submit_audio_question(
    audio_file: UploadFile = File(...),
    context_images: Optional[str] = Form(None),  # JSON string of UUIDs
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Submit an audio question."""
    # Validate audio file
    QuestionService.validate_audio_file(audio_file)

    # Parse context images
    context_image_objects = []
    if context_images:
        try:
            import json

            image_ids = json.loads(context_images)
            context_image_objects = (
                db.query(Image).filter(Image.id.in_(image_ids), Image.user_id == current_user.id).all()
            )
        except (json.JSONDecodeError, ValueError):
            pass

    # Read and save audio file
    file_content = await audio_file.read()
    audio_path, audio_url = await QuestionService.save_audio_file(
        file_content, audio_file.filename, str(current_user.id)
    )

    # Transcribe audio to text
    try:
        transcribed_text = await QuestionService.transcribe_audio(audio_path)
    except HTTPException:
        # If transcription fails, create question with placeholder text
        transcribed_text = "[Audio transcription failed - audio file available for playback]"

    # Create question
    question = Question(
        content=transcribed_text,
        type=QuestionType.AUDIO,
        audio_filename=audio_file.filename,
        audio_file_path=audio_path,
        audio_url=audio_url,
        user_id=current_user.id,
    )

    # Add context images
    if context_image_objects:
        question.context_images = context_image_objects

    db.add(question)
    db.commit()
    db.refresh(question)

    # Generate answer in background
    background_tasks.add_task(QuestionService.generate_answer, question, context_image_objects, db)

    return QuestionResponse.from_orm(question)


@router.get("", response_model=QuestionListResponse)
async def get_user_questions(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get question history for authenticated user."""
    # Calculate offset
    offset = (page - 1) * limit

    # Get total count
    total = db.query(Question).filter(Question.user_id == current_user.id).count()

    # Get questions
    questions = (
        db.query(Question)
        .filter(Question.user_id == current_user.id)
        .order_by(Question.created_at.desc())
        .offset(offset)
        .limit(limit)
        .all()
    )

    # Calculate pagination info
    pages = math.ceil(total / limit)
    has_next = page < pages
    has_prev = page > 1

    pagination = PaginationInfo(
        page=page, limit=limit, total=total, pages=pages, has_next=has_next, has_prev=has_prev
    )

    return QuestionListResponse(
        questions=[QuestionResponse.from_orm(q) for q in questions], pagination=pagination
    )


@router.get("/{question_id}", response_model=QuestionDetailResponse)
async def get_question_details(
    question_id: UUID, current_user: User = Depends(get_current_user), db: Session = Depends(get_db)
):
    """Get specific question and its answer."""
    question = (
        db.query(Question).filter(Question.id == question_id, Question.user_id == current_user.id).first()
    )

    if not question:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Question not found")

    # Convert to response model
    response_data = QuestionResponse.from_orm(question).dict()

    # Add answer if available
    if question.answer:
        from app.schemas import AnswerResponse

        response_data["answer"] = AnswerResponse.from_orm(question.answer)

    return QuestionDetailResponse(**response_data)

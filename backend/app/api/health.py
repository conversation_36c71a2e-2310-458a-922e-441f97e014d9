"""Health check API routes."""

from datetime import datetime

from fastapi import APIRouter

from app.config import settings
from app.schemas import HealthResponse

router = APIRouter(prefix="/health", tags=["System"])


@router.get("", response_model=HealthResponse)
async def health_check():
    """Check API health status."""
    return HealthResponse(status="healthy", timestamp=datetime.utcnow(), version=settings.APP_VERSION)

"""Database API endpoints."""

from typing import List
from uuid import UUID

import structlog
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.auth.dependencies import get_current_user
from app.database import get_db
from app.models import User
from app.schemas import (
    DatabaseCloneRequest,
    DatabaseDetailResponse,
    DatabaseListResponse,
    DatabaseRequest,
    DatabaseResponse,
    DatabaseStatsResponse,
    DatabaseUpdateRequest,
    MessageResponse,
    PaginationInfo,
)
from app.services.database_service import DatabaseService

logger = structlog.get_logger()

router = APIRouter(prefix="/databases", tags=["databases"])


@router.post("/", response_model=DatabaseResponse, status_code=status.HTTP_201_CREATED)
async def create_database(
    database_data: DatabaseRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Create a new customizable database."""
    try:
        service = DatabaseService(db)
        database = service.create_database(current_user.id, database_data)
        
        logger.info(
            "Database created",
            database_id=str(database.id),
            user_id=str(current_user.id),
            name=database.name,
        )
        
        return DatabaseResponse.from_orm(database)
    
    except Exception as e:
        logger.error("Failed to create database", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create database: {str(e)}"
        )


@router.get("/", response_model=DatabaseListResponse)
async def get_databases(
    skip: int = 0,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get all databases for the current user."""
    try:
        service = DatabaseService(db)
        databases, total = service.get_user_databases(current_user.id, skip, limit)
        
        return DatabaseListResponse(
            databases=[DatabaseResponse.from_orm(db) for db in databases],
            pagination=PaginationInfo(
                page=skip // limit + 1,
                page_size=limit,
                total_items=total,
                total_pages=(total + limit - 1) // limit,
            ),
        )
    
    except Exception as e:
        logger.error("Failed to get databases", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve databases"
        )


@router.get("/stats", response_model=List[DatabaseStatsResponse])
async def get_database_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get statistics for all user databases."""
    try:
        service = DatabaseService(db)
        stats = service.get_database_stats(current_user.id)
        
        return [DatabaseStatsResponse(**stat) for stat in stats]
    
    except Exception as e:
        logger.error("Failed to get database stats", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve database statistics"
        )


@router.get("/search")
async def search_databases(
    q: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Search databases by name or description."""
    try:
        service = DatabaseService(db)
        databases = service.search_databases(current_user.id, q)
        
        return [DatabaseResponse.from_orm(db) for db in databases]
    
    except Exception as e:
        logger.error("Failed to search databases", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search databases"
        )


@router.get("/recent")
async def get_recent_databases(
    limit: int = 5,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get recently updated databases."""
    try:
        service = DatabaseService(db)
        databases = service.get_recent_databases(current_user.id, limit)
        
        return [DatabaseResponse.from_orm(db) for db in databases]
    
    except Exception as e:
        logger.error("Failed to get recent databases", error=str(e), user_id=str(current_user.id))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve recent databases"
        )


@router.get("/{database_id}", response_model=DatabaseDetailResponse)
async def get_database(
    database_id: UUID,
    include_records: bool = False,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Get a specific database with its fields and optionally records."""
    try:
        service = DatabaseService(db)
        database = service.get_database(database_id, current_user.id)
        
        if not database:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Database not found"
            )
        
        # Convert to response format
        response_data = database.to_dict(include_fields=True, include_records=include_records)
        
        return DatabaseDetailResponse(**response_data)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to get database",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve database"
        )


@router.put("/{database_id}", response_model=DatabaseResponse)
async def update_database(
    database_id: UUID,
    update_data: DatabaseUpdateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Update a database."""
    try:
        service = DatabaseService(db)
        database = service.update_database(database_id, current_user.id, update_data)
        
        if not database:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Database not found"
            )
        
        logger.info(
            "Database updated",
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        
        return DatabaseResponse.from_orm(database)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to update database",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update database: {str(e)}"
        )


@router.delete("/{database_id}", response_model=MessageResponse)
async def delete_database(
    database_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Delete a database."""
    try:
        service = DatabaseService(db)
        success = service.delete_database(database_id, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Database not found"
            )
        
        logger.info(
            "Database deleted",
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        
        return MessageResponse(message="Database deleted successfully")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to delete database",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete database"
        )


@router.post("/{database_id}/clone", response_model=DatabaseResponse)
async def clone_database(
    database_id: UUID,
    clone_data: DatabaseCloneRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """Clone a database with its fields and optionally records."""
    try:
        service = DatabaseService(db)
        new_database = service.clone_database(
            database_id,
            current_user.id,
            clone_data.name,
            clone_data.include_records,
            clone_data.description,
        )
        
        if not new_database:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Source database not found"
            )
        
        logger.info(
            "Database cloned",
            source_database_id=str(database_id),
            new_database_id=str(new_database.id),
            user_id=str(current_user.id),
            include_records=clone_data.include_records,
        )
        
        return DatabaseResponse.from_orm(new_database)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to clone database",
            error=str(e),
            database_id=str(database_id),
            user_id=str(current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to clone database: {str(e)}"
        )

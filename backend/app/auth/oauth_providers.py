"""OAuth provider implementations."""

import json
from abc import ABC, abstractmethod
from typing import Dict, Optional

import httpx
from authlib.jose import jwt
from authlib.jose.errors import Jose<PERSON>rror

from app.config import settings


class OAuthProvider(ABC):
    """Abstract base class for OAuth providers."""

    @abstractmethod
    async def verify_token(self, token_data: Dict) -> Optional[Dict]:
        """Verify OAuth token and return user info."""
        pass


class AppleOAuth(OAuthProvider):
    """Apple Sign-In OAuth provider."""

    APPLE_KEYS_URL = "https://appleid.apple.com/auth/keys"
    APPLE_ISSUER = "https://appleid.apple.com"

    async def verify_token(self, token_data: Dict) -> Optional[Dict]:
        """Verify Apple ID token."""
        try:
            identity_token = token_data.get("identity_token")
            if not identity_token:
                return None

            # Get Apple's public keys
            async with httpx.AsyncClient() as client:
                response = await client.get(self.APPLE_KEYS_URL)
                if response.status_code != 200:
                    return None

                keys = response.json()

            # Verify and decode the token
            try:
                claims = jwt.decode(
                    identity_token,
                    keys,
                    claims_options={
                        "iss": {"essential": True, "value": self.APPLE_ISSUER},
                        "aud": {"essential": True, "value": settings.APPLE_CLIENT_ID},
                    },
                )
            except JoseError:
                return None

            # Extract user information
            user_info = {
                "provider_id": claims.get("sub"),
                "email": claims.get("email"),
                "email_verified": claims.get("email_verified", False),
                "name": None,
                "first_name": None,
                "last_name": None,
            }

            # Apple sometimes provides user info in the request
            if "user_info" in token_data:
                apple_user_info = token_data["user_info"]
                if "name" in apple_user_info:
                    name_info = apple_user_info["name"]
                    user_info["first_name"] = name_info.get("firstName")
                    user_info["last_name"] = name_info.get("lastName")
                    if user_info["first_name"] and user_info["last_name"]:
                        user_info["name"] = f"{user_info['first_name']} {user_info['last_name']}"

                if "email" in apple_user_info:
                    user_info["email"] = apple_user_info["email"]

            return user_info

        except Exception:
            return None


class GoogleOAuth(OAuthProvider):
    """Google Sign-In OAuth provider."""

    GOOGLE_USERINFO_URL = "https://www.googleapis.com/oauth2/v2/userinfo"
    GOOGLE_TOKENINFO_URL = "https://oauth2.googleapis.com/tokeninfo"

    async def verify_token(self, token_data: Dict) -> Optional[Dict]:
        """Verify Google ID token."""
        try:
            id_token = token_data.get("id_token")
            access_token = token_data.get("access_token")

            if not id_token:
                return None

            # Verify ID token
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.GOOGLE_TOKENINFO_URL}?id_token={id_token}")

                if response.status_code != 200:
                    return None

                token_info = response.json()

                # Verify audience (client ID)
                if token_info.get("aud") != settings.GOOGLE_CLIENT_ID:
                    return None

                # Get additional user info if access token is provided
                user_info = {
                    "provider_id": token_info.get("sub"),
                    "email": token_info.get("email"),
                    "email_verified": token_info.get("email_verified") == "true",
                    "name": token_info.get("name"),
                    "first_name": token_info.get("given_name"),
                    "last_name": token_info.get("family_name"),
                    "avatar_url": token_info.get("picture"),
                }

                return user_info

        except Exception:
            return None


class MicrosoftOAuth(OAuthProvider):
    """Microsoft Sign-In OAuth provider."""

    MICROSOFT_GRAPH_URL = "https://graph.microsoft.com/v1.0/me"
    MICROSOFT_TOKENINFO_URL = "https://login.microsoftonline.com/common/oauth2/v2.0/token"

    async def verify_token(self, token_data: Dict) -> Optional[Dict]:
        """Verify Microsoft access token."""
        try:
            access_token = token_data.get("access_token")
            if not access_token:
                return None

            # Get user info from Microsoft Graph
            async with httpx.AsyncClient() as client:
                headers = {"Authorization": f"Bearer {access_token}"}
                response = await client.get(self.MICROSOFT_GRAPH_URL, headers=headers)

                if response.status_code != 200:
                    return None

                user_data = response.json()

                user_info = {
                    "provider_id": user_data.get("id"),
                    "email": user_data.get("mail") or user_data.get("userPrincipalName"),
                    "email_verified": True,  # Microsoft emails are considered verified
                    "name": user_data.get("displayName"),
                    "first_name": user_data.get("givenName"),
                    "last_name": user_data.get("surname"),
                }

                return user_info

        except Exception:
            return None


# Provider instances
apple_oauth = AppleOAuth()
google_oauth = GoogleOAuth()
microsoft_oauth = MicrosoftOAuth()

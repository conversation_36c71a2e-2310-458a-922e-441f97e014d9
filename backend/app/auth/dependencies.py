"""Authentication dependencies for FastAPI."""

from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from sqlalchemy.orm import Session

from app.auth.jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.database import get_db
from app.models import User

# Security scheme
security = HTTPBearer()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Verify token
        payload = JWTHandler.verify_token(credentials.credentials)
        if payload is None:
            raise credentials_exception

        # Check token type
        if payload.get("type") != "access":
            raise credentials_exception

        # Get user ID
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception

        # Check if token is revoked
        jti = payload.get("jti")
        if jti and J<PERSON><PERSON><PERSON><PERSON>.is_token_revoked(db, jti):
            raise credentials_exception

    except Exception:
        raise credentials_exception

    # Get user from database
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise credentials_exception

    return user


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: Session = Depends(get_db),
) -> Optional[User]:
    """Get current authenticated user (optional)."""
    if not credentials:
        return None

    try:
        # Verify token
        payload = JWTHandler.verify_token(credentials.credentials)
        if payload is None:
            return None

        # Check token type
        if payload.get("type") != "access":
            return None

        # Get user ID
        user_id: str = payload.get("sub")
        if user_id is None:
            return None

        # Check if token is revoked
        jti = payload.get("jti")
        if jti and JWTHandler.is_token_revoked(db, jti):
            return None

        # Get user from database
        user = db.query(User).filter(User.id == user_id).first()
        return user

    except Exception:
        return None


def require_auth(user: User = Depends(get_current_user)) -> User:
    """Dependency that requires authentication."""
    return user


def optional_auth(user: Optional[User] = Depends(get_current_user_optional)) -> Optional[User]:
    """Dependency for optional authentication."""
    return user

"""Application configuration settings."""

import os
from pathlib import Path
from typing import List, Optional

import dotenv
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


def get_repo_dir():
    """Get repository directory."""
    repo_dir = Path(os.path.dirname(os.path.abspath(__file__)) + "/../../").resolve()
    return repo_dir


repo_dir = get_repo_dir()


class Settings(BaseSettings):
    """Application settings."""

    # Database
    DATABASE_URL: str = Field(default="sqlite:///./wheresz.db")
    DATABASE_URL_ASYNC: str = Field(default="sqlite+aiosqlite:///./wheresz.db")

    # Supabase Configuration
    SUPABASE_URL: Optional[str] = None
    SUPABASE_ANON_KEY: Optional[str] = None
    SUPABASE_SERVICE_ROLE_KEY: Optional[str] = None
    USE_SUPABASE: bool = Field(default=False)

    # JWT Configuration
    JWT_SECRET_KEY: str = Field(default="your-super-secret-jwt-key-change-this-in-production")
    JWT_ALGORITHM: str = Field(default="HS256")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30)
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7)

    # OAuth Configuration
    # Apple Sign-In
    APPLE_CLIENT_ID: Optional[str] = None
    APPLE_TEAM_ID: Optional[str] = None
    APPLE_KEY_ID: Optional[str] = None
    APPLE_PRIVATE_KEY_PATH: Optional[str] = None

    # Google Sign-In
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None

    # Microsoft Sign-In
    MICROSOFT_CLIENT_ID: Optional[str] = None
    MICROSOFT_CLIENT_SECRET: Optional[str] = None
    MICROSOFT_TENANT_ID: str = Field(default="common")

    # File Upload Configuration
    UPLOAD_DIR: str = Field(default="uploads")
    MAX_FILE_SIZE: int = Field(default=10485760)  # 10MB
    ALLOWED_IMAGE_EXTENSIONS: str = Field(default="jpg,jpeg,png,gif,webp")
    ALLOWED_AUDIO_EXTENSIONS: str = Field(default="mp3,wav,m4a,aac")

    # Image Processing
    IMAGE_QUALITY: int = Field(default=85)
    THUMBNAIL_SIZE: str = Field(default="300,300")

    # Audio Processing
    SPEECH_TO_TEXT_SERVICE: str = Field(default="openai")
    GOOGLE_SPEECH_API_KEY: Optional[str] = None

    # LLM and AI Services Configuration
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = Field(default="gpt-4")
    OPENAI_VISION_MODEL: str = Field(default="gpt-4-vision-preview")
    OPENAI_EMBEDDING_MODEL: str = Field(default="text-embedding-3-small")
    OPENAI_WHISPER_MODEL: str = Field(default="whisper-1")

    # Alternative LLM providers (optional)
    ANTHROPIC_API_KEY: Optional[str] = None
    ANTHROPIC_MODEL: str = Field(default="claude-3-sonnet-20240229")

    # Embedding and Vector Search Configuration
    EMBEDDING_DIMENSION: int = Field(default=1536)  # OpenAI text-embedding-3-small
    SIMILARITY_THRESHOLD: float = Field(default=0.7)
    MAX_CONTEXT_LENGTH: int = Field(default=4000)
    MAX_EMBEDDING_BATCH_SIZE: int = Field(default=100)

    # CORS Configuration
    CORS_ORIGINS: str = Field(default="http://localhost:3000,http://localhost:19006,exp://localhost:19000")

    # Application Configuration
    APP_NAME: str = Field(default="WhereZ API")
    APP_VERSION: str = Field(default="1.0.0")
    DEBUG: bool = Field(default=True)
    LOG_LEVEL: str = Field(default="INFO")

    # External Services (Optional)
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    AWS_REGION: str = Field(default="us-east-1")
    AWS_S3_BUCKET: Optional[str] = None

    REDIS_URL: Optional[str] = None

    model_config = SettingsConfigDict(
        env_file=str(repo_dir / "backend/.env"),
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore",
    )

    @property
    def cors_origins_list(self) -> List[str]:
        """Get CORS origins as a list."""
        return [origin.strip() for origin in self.CORS_ORIGINS.split(",")]

    @property
    def allowed_image_extensions_list(self) -> List[str]:
        """Get allowed image extensions as a list."""
        return [ext.strip().lower() for ext in self.ALLOWED_IMAGE_EXTENSIONS.split(",")]

    @property
    def allowed_audio_extensions_list(self) -> List[str]:
        """Get allowed audio extensions as a list."""
        return [ext.strip().lower() for ext in self.ALLOWED_AUDIO_EXTENSIONS.split(",")]

    @property
    def thumbnail_size_tuple(self) -> tuple:
        """Get thumbnail size as a tuple."""
        width, height = self.THUMBNAIL_SIZE.split(",")
        return (int(width.strip()), int(height.strip()))


# Create settings instance
settings = Settings()
a = 0

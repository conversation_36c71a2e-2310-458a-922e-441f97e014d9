"""Processed content model."""

import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from sqlalchemy import JSO<PERSON>, Column, DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Float, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import ARRAY, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database import Base
from app.models.content_embedding import ContentType


class ProcessingStatus(str, Enum):
    """Processing status enumeration."""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ProcessedContent(Base):
    """Processed content model for storing LLM analysis results."""

    __tablename__ = "processed_content"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Content reference
    content_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    content_type = Column(SQLEnum(ContentType), nullable=False, index=True)

    # Content data
    original_content = Column(Text, nullable=True)
    processed_content = Column(Text, nullable=False)
    summary = Column(Text, nullable=True)
    key_points = Column(ARRAY(Text), nullable=True)
    tags = Column(ARRAY(String(100)), nullable=True)

    # Analysis results
    sentiment = Column(String(50), nullable=True)
    confidence = Column(Float, nullable=True)
    llm_model = Column(String(100), nullable=True)

    # Processing metadata
    processing_metadata = Column(JSON, nullable=True)

    # Foreign key to user
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    user = relationship("User", back_populates="processed_content")

    def __repr__(self) -> str:
        return f"<ProcessedContent(id={self.id}, content_type={self.content_type}, user_id={self.user_id})>"

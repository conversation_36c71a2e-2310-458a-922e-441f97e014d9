"""Record model for database records."""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import Column, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database import Base


class Record(Base):
    """Record model for storing database records with JSONB properties."""

    __tablename__ = "records"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Record data
    properties = Column(JSONB, nullable=False, default=dict)  # { "<field_id>": <value>, ... }

    # Foreign keys
    database_id = Column(
        UUID(as_uuid=True), ForeignKey("databases.id", ondelete="CASCADE"), nullable=False, index=True
    )
    created_by = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    database = relationship("Database", back_populates="records")
    creator = relationship("User", back_populates="records")

    def __repr__(self) -> str:
        return f"<Record(id={self.id}, database_id={self.database_id}, created_by={self.created_by})>"

    def to_dict(self, include_field_names: bool = False) -> dict:
        """Convert record to dictionary."""
        data = {
            "id": str(self.id),
            "properties": self.properties or {},
            "database_id": str(self.database_id),
            "created_by": str(self.created_by),
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

        # Optionally include field names for easier frontend consumption
        if include_field_names and self.database and self.database.fields:
            field_map = {str(field.id): field.name for field in self.database.fields}
            data["properties_with_names"] = {
                field_map.get(field_id, field_id): value
                for field_id, value in (self.properties or {}).items()
            }

        return data

    def get_property_value(self, field_id: str) -> Any:
        """Get property value by field ID."""
        return (self.properties or {}).get(field_id)

    def set_property_value(self, field_id: str, value: Any) -> None:
        """Set property value by field ID."""
        if self.properties is None:
            self.properties = {}
        self.properties[field_id] = value

    def validate_properties(self, fields: List["Field"]) -> tuple[bool, List[str]]:
        """Validate all properties against field definitions."""
        errors = []
        field_map = {str(field.id): field for field in fields}
        
        # Validate each property
        for field_id, value in (self.properties or {}).items():
            field = field_map.get(field_id)
            if field:
                is_valid, error = field.validate_value(value)
                if not is_valid:
                    errors.append(error)
            else:
                errors.append(f"Unknown field ID: {field_id}")

        # Check for required fields that are missing
        for field in fields:
            field_id = str(field.id)
            settings = field.settings or {}
            if settings.get("required", False) and field_id not in (self.properties or {}):
                errors.append(f"Required field '{field.name}' is missing")

        return len(errors) == 0, errors

    def get_display_value(self, field_id: str, field: Optional["Field"] = None) -> str:
        """Get a human-readable display value for a property."""
        value = self.get_property_value(field_id)
        
        if value is None:
            return ""
        
        if field:
            # Format based on field type
            if field.type.value == "checkbox":
                return "Yes" if value else "No"
            elif field.type.value == "multi_select":
                return ", ".join(value) if isinstance(value, list) else str(value)
            elif field.type.value == "date":
                if isinstance(value, str):
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        return dt.strftime("%Y-%m-%d %H:%M" if "T" in value else "%Y-%m-%d")
                    except:
                        return str(value)
        
        return str(value)

    def search_text_content(self) -> str:
        """Get all text content for full-text search."""
        content_parts = []
        
        for value in (self.properties or {}).values():
            if isinstance(value, str):
                content_parts.append(value)
            elif isinstance(value, list):
                content_parts.extend([str(item) for item in value])
            else:
                content_parts.append(str(value))
        
        return " ".join(content_parts)

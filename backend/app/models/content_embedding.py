"""Content embedding model."""

import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional

from pgvector.sqlalchemy import Vector
from sqlalchemy import JSO<PERSON>, Column, DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import Foreign<PERSON><PERSON>, String, Text
from sqlalchemy.dialects.postgresql import ARRAY, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database import Base


class ContentType(str, Enum):
    """Content type enumeration."""

    TEXT = "text"
    IMAGE = "image"
    AUDIO = "audio"
    DOCUMENT = "document"


class ContentEmbedding(Base):
    """Content embedding model for storing vector embeddings."""

    __tablename__ = "content_embeddings"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Content reference
    content_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    content_type = Column(SQLEnum(ContentType), nullable=False, index=True)

    # Content and embedding
    content_text = Column(Text, nullable=False)
    embedding = Column(Vector(1536), nullable=True)  # OpenAI embedding dimension

    # Metadata (using content_metadata to avoid SQLAlchemy reserved name)
    content_metadata = Column("metadata", JSON, nullable=True)

    # Foreign key to user
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    user = relationship("User", back_populates="content_embeddings")

    def __repr__(self) -> str:
        return f"<ContentEmbedding(id={self.id}, content_type={self.content_type}, user_id={self.user_id})>"

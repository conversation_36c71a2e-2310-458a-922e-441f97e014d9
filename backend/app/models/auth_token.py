"""Authentication token model."""

import uuid
from datetime import datetime
from enum import Enum

from sqlalchemy import Column, DateTime
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database import Base


class TokenType(str, Enum):
    """Token type enumeration."""

    ACCESS = "access"
    REFRESH = "refresh"


class AuthToken(Base):
    """Authentication token model for managing JWT tokens."""

    __tablename__ = "auth_tokens"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Token information
    token = Column(Text, nullable=False, unique=True, index=True)
    token_type = Column(SQLEnum(TokenType), nullable=False, default=TokenType.ACCESS)

    # Token metadata
    jti = Column(String(255), nullable=False, unique=True, index=True)  # JWT ID
    is_revoked = Column(String(1), nullable=False, default="0")  # "0" = False, "1" = True

    # Expiration
    expires_at = Column(DateTime(timezone=True), nullable=False)

    # Foreign key to user
    user_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    user = relationship("User", back_populates="auth_tokens")

    def __repr__(self) -> str:
        return f"<AuthToken(id={self.id}, type={self.token_type}, user_id={self.user_id}, revoked={self.is_revoked})>"

    @property
    def is_revoked_bool(self) -> bool:
        """Get revoked status as boolean."""
        return self.is_revoked == "1"

    def revoke(self) -> None:
        """Revoke the token."""
        self.is_revoked = "1"

    def is_expired(self) -> bool:
        """Check if token is expired."""
        return datetime.utcnow() > self.expires_at

    def is_valid(self) -> bool:
        """Check if token is valid (not revoked and not expired)."""
        return not self.is_revoked_bool and not self.is_expired()

    def to_dict(self) -> dict:
        """Convert token to dictionary."""
        return {
            "id": str(self.id),
            "token_type": self.token_type.value,
            "jti": self.jti,
            "is_revoked": self.is_revoked_bool,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "user_id": str(self.user_id),
        }

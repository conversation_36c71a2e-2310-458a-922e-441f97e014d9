"""Field model for database field definitions."""

import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional

from sqlalchemy import Column, DateTime, ForeignKey, Integer, String, UniqueConstraint
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database import Base


class FieldType(str, Enum):
    """Field type enumeration."""

    TEXT = "text"
    NUMBER = "number"
    DATE = "date"
    SELECT = "select"
    MULTI_SELECT = "multi_select"
    CHECKBOX = "checkbox"
    URL = "url"
    EMAIL = "email"
    PHONE = "phone"
    RICH_TEXT = "rich_text"


class Field(Base):
    """Field model for storing database field definitions."""

    __tablename__ = "fields"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Field metadata
    name = Column(String(255), nullable=False)
    type = Column(SQLEnum(FieldType), nullable=False, index=True)
    settings = Column(JSONB, nullable=True)  # Field-specific settings
    position = Column(Integer, nullable=False, default=0)

    # Foreign key to database
    database_id = Column(
        UUID(as_uuid=True), ForeignKey("databases.id", ondelete="CASCADE"), nullable=False, index=True
    )

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    database = relationship("Database", back_populates="fields")

    # Constraints
    __table_args__ = (UniqueConstraint("database_id", "name", name="uq_database_field_name"),)

    def __repr__(self) -> str:
        return f"<Field(id={self.id}, name={self.name}, type={self.type}, database_id={self.database_id})>"

    def to_dict(self) -> dict:
        """Convert field to dictionary."""
        return {
            "id": str(self.id),
            "name": self.name,
            "type": self.type.value,
            "settings": self.settings or {},
            "position": self.position,
            "database_id": str(self.database_id),
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

    def get_default_settings(self) -> Dict[str, Any]:
        """Get default settings for field type."""
        defaults = {
            FieldType.TEXT: {"required": False, "max_length": None},
            FieldType.NUMBER: {"required": False, "min_value": None, "max_value": None, "decimal_places": 0},
            FieldType.DATE: {"required": False, "include_time": False},
            FieldType.SELECT: {"required": False, "options": []},
            FieldType.MULTI_SELECT: {"required": False, "options": []},
            FieldType.CHECKBOX: {"required": False, "default_value": False},
            FieldType.URL: {"required": False},
            FieldType.EMAIL: {"required": False},
            FieldType.PHONE: {"required": False},
            FieldType.RICH_TEXT: {"required": False},
        }
        return defaults.get(self.type, {"required": False})

    def validate_value(self, value: Any) -> tuple[bool, Optional[str]]:
        """Validate a value against this field's type and settings."""
        settings = self.settings or {}
        
        # Check if required
        if settings.get("required", False) and (value is None or value == ""):
            return False, f"Field '{self.name}' is required"

        # If value is None or empty and not required, it's valid
        if value is None or value == "":
            return True, None

        # Type-specific validation
        try:
            if self.type == FieldType.TEXT:
                if not isinstance(value, str):
                    return False, f"Field '{self.name}' must be text"
                max_length = settings.get("max_length")
                if max_length and len(value) > max_length:
                    return False, f"Field '{self.name}' exceeds maximum length of {max_length}"

            elif self.type == FieldType.NUMBER:
                if not isinstance(value, (int, float)):
                    try:
                        value = float(value)
                    except (ValueError, TypeError):
                        return False, f"Field '{self.name}' must be a number"
                
                min_val = settings.get("min_value")
                max_val = settings.get("max_value")
                if min_val is not None and value < min_val:
                    return False, f"Field '{self.name}' must be at least {min_val}"
                if max_val is not None and value > max_val:
                    return False, f"Field '{self.name}' must be at most {max_val}"

            elif self.type == FieldType.DATE:
                # Accept ISO date strings or datetime objects
                if isinstance(value, str):
                    from datetime import datetime
                    try:
                        datetime.fromisoformat(value.replace('Z', '+00:00'))
                    except ValueError:
                        return False, f"Field '{self.name}' must be a valid date"

            elif self.type == FieldType.SELECT:
                options = settings.get("options", [])
                if value not in options:
                    return False, f"Field '{self.name}' must be one of: {', '.join(options)}"

            elif self.type == FieldType.MULTI_SELECT:
                if not isinstance(value, list):
                    return False, f"Field '{self.name}' must be a list"
                options = settings.get("options", [])
                for item in value:
                    if item not in options:
                        return False, f"Field '{self.name}' contains invalid option: {item}"

            elif self.type == FieldType.CHECKBOX:
                if not isinstance(value, bool):
                    return False, f"Field '{self.name}' must be true or false"

            elif self.type in [FieldType.URL, FieldType.EMAIL]:
                if not isinstance(value, str):
                    return False, f"Field '{self.name}' must be text"
                # Basic validation - could be enhanced with regex
                if self.type == FieldType.EMAIL and "@" not in value:
                    return False, f"Field '{self.name}' must be a valid email address"
                if self.type == FieldType.URL and not value.startswith(("http://", "https://")):
                    return False, f"Field '{self.name}' must be a valid URL"

            return True, None

        except Exception as e:
            return False, f"Validation error for field '{self.name}': {str(e)}"

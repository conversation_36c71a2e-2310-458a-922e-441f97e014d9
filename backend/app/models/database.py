"""Database model for customizable databases."""

import uuid
from datetime import datetime
from typing import List, Optional

from sqlalchemy import Column, DateTime, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.database import Base


class Database(Base):
    """Database model for storing user-defined databases."""

    __tablename__ = "databases"

    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)

    # Database metadata
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    icon = Column(String(100), nullable=True)  # emoji or icon name
    color = Column(String(7), nullable=True)  # hex color code

    # Foreign key to user (owner)
    owner_id = Column(
        UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True
    )

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    owner = relationship("User", back_populates="databases")
    fields = relationship("Field", back_populates="database", cascade="all, delete-orphan")
    records = relationship("Record", back_populates="database", cascade="all, delete-orphan")

    def __repr__(self) -> str:
        return f"<Database(id={self.id}, name={self.name}, owner_id={self.owner_id})>"

    def to_dict(self, include_fields: bool = False, include_records: bool = False) -> dict:
        """Convert database to dictionary."""
        data = {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "icon": self.icon,
            "color": self.color,
            "owner_id": str(self.owner_id),
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }

        if include_fields and self.fields:
            data["fields"] = [field.to_dict() for field in sorted(self.fields, key=lambda f: f.position)]

        if include_records and self.records:
            data["records"] = [record.to_dict() for record in self.records]

        return data

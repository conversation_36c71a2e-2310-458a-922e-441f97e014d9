[build-system]
requires = [
  "setuptools >= 40.9.0, < 70.0",
  "wheel",
]
build-backend = "setuptools.build_meta"

[flake8]
exclude = '''
'''
max-line-length = 110

[tool.isort]
known_third_party = []
line_length = 110
profile = "black"
skip_glob = "*_pb2.py"

[tool.black]
line-length = 110
target-version = ["py311"]
extend-exclude = '''
()
'''

[tool.pytest.ini_options]
testpaths = [
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "no_parallel: marks tests as needing to be run with a single test worker",
]

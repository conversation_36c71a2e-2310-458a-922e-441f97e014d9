# WhereZ Backend API

FastAPI backend for the WhereZ mobile application MVP with Supabase integration support.

## Features

- **Supabase Integration**: Full support for Supabase backend-as-a-service
- **Hybrid Architecture**: Can work with Supabase or as standalone FastAPI backend
- **OAuth2 Authentication**: Apple Sign-In, Google Sign-In, Microsoft Sign-In
- **JWT Token Management**: Access and refresh tokens with database storage
- **Image Upload & Processing**: File validation, compression, thumbnail generation with Supabase Storage
- **Question & Answer System**: Text and audio input processing with mock AI responses
- **User Profile Management**: Basic user information and preferences
- **RESTful API**: Complete OpenAPI/Swagger documentation

## Technology Stack

- **Framework**: FastAPI 0.104+
- **Backend-as-a-Service**: Supabase (PostgreSQL + Auth + Storage + Real-time)
- **Database**: SQLAlchemy ORM with PostgreSQL/SQLite support
- **Authentication**: OAuth2 + JWT with python-jose + Supabase Auth
- **Storage**: Supabase Storage + Local file storage fallback
- **Image Processing**: Pillow (PIL)
- **Audio Processing**: SpeechRecognition + pydub
- **Async Support**: Full async/await support
- **Logging**: Structured logging with structlog

## Quick Start

### Prerequisites

- Python 3.9+
- PostgreSQL (for production) or SQLite (for development)
- Virtual environment (recommended)

### Installation

1. **Clone and navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Run the application**:
   ```bash
   python -m uvicorn app.main:app --reload
   ```

The API will be available at `http://localhost:8000`

## Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/wheresz_db
DATABASE_URL_ASYNC=postgresql+asyncpg://user:pass@localhost:5432/wheresz_db

# JWT Security
JWT_SECRET_KEY=your-super-secret-key
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
APPLE_CLIENT_ID=your.app.bundle.id
MICROSOFT_CLIENT_ID=your-microsoft-client-id

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB
```

### OAuth Provider Setup

#### Google Sign-In
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add your domain to authorized origins

#### Apple Sign-In
1. Go to [Apple Developer Portal](https://developer.apple.com/)
2. Create App ID with Sign In with Apple capability
3. Create Service ID for web authentication
4. Generate private key for authentication
5. Configure return URLs

#### Microsoft Sign-In
1. Go to [Azure Portal](https://portal.azure.com/)
2. Register new application in Azure AD
3. Configure authentication platform
4. Generate client secret
5. Set up API permissions

## API Documentation

### Interactive Documentation

- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`
- **OpenAPI Spec**: See `../docs/api-spec.yaml`

### Key Endpoints

#### Authentication
- `POST /auth/oauth/apple` - Apple Sign-In
- `POST /auth/oauth/google` - Google Sign-In  
- `POST /auth/oauth/microsoft` - Microsoft Sign-In
- `POST /auth/refresh` - Refresh access token
- `POST /auth/logout` - Logout and revoke tokens

#### Users
- `GET /users/me` - Get current user profile
- `PUT /users/me` - Update user profile

#### Images
- `POST /images/upload` - Upload image
- `GET /images/{image_id}` - Get image details
- `GET /images/user` - Get user's images
- `DELETE /images/{image_id}` - Delete image

#### Questions & Answers
- `POST /questions` - Submit text question
- `POST /questions/audio` - Submit audio question
- `GET /questions` - Get question history
- `GET /questions/{question_id}` - Get question details
- `GET /answers/{question_id}` - Get answer for question

## Database

### Models

- **User**: OAuth user information and profile
- **AuthToken**: JWT token storage and management
- **Image**: Uploaded image metadata and file paths
- **Question**: User questions (text/audio) with context
- **Answer**: Generated answers with confidence scores

### Migrations

The application uses SQLAlchemy with automatic table creation. For production, consider using Alembic for migrations:

```bash
# Install Alembic
pip install alembic

# Initialize migrations
alembic init migrations

# Create migration
alembic revision --autogenerate -m "Initial migration"

# Apply migration
alembic upgrade head
```

## File Storage

### Local Storage (Development)

Files are stored in the `uploads/` directory:
```
uploads/
├── {user_id}/
│   ├── image1.jpg
│   ├── image1_thumb.jpg
│   └── ...
└── audio/
    └── {user_id}/
        ├── audio1.wav
        └── ...
```

### Cloud Storage (Production)

For production, configure AWS S3 or similar:

```env
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=wheresz-uploads
AWS_REGION=us-east-1
```

## Testing

### Run Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio pytest-cov httpx

# Run tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html
```

### Test Structure

```
tests/
├── test_auth.py          # Authentication tests
├── test_images.py        # Image upload tests
├── test_questions.py     # Q&A system tests
├── test_users.py         # User management tests
└── conftest.py           # Test configuration
```

## Deployment

### Docker Deployment

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Production Checklist

- [ ] Set `DEBUG=false`
- [ ] Use PostgreSQL database
- [ ] Configure proper JWT secrets
- [ ] Set up OAuth provider credentials
- [ ] Configure cloud file storage
- [ ] Set up SSL/HTTPS
- [ ] Configure logging and monitoring
- [ ] Set up database backups
- [ ] Configure rate limiting
- [ ] Set up health checks

## Monitoring & Logging

### Structured Logging

The application uses structured logging with JSON output:

```python
import structlog
logger = structlog.get_logger()

logger.info("User authenticated", user_id=user.id, provider="google")
```

### Health Checks

- `GET /health` - Basic health check
- Database connectivity check
- File system access check

## Security

### Authentication Flow

1. Client authenticates with OAuth provider
2. Client sends OAuth token to backend
3. Backend verifies token with provider
4. Backend creates/updates user record
5. Backend generates JWT access/refresh tokens
6. Client uses JWT for subsequent requests

### Security Features

- JWT token blacklisting
- Request rate limiting (recommended)
- File type validation
- File size limits
- CORS configuration
- SQL injection prevention (SQLAlchemy ORM)
- XSS protection (FastAPI built-in)

## Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit pull request


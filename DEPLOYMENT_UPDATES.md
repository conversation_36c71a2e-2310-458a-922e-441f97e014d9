# WhereZ - Deployment Updates Guide

This guide covers how to update your deployed WhereZ application, with special focus on database changes, migrations, and safe deployment practices.

## 🏗️ Deployment Architecture

WhereZ uses Supabase as the primary backend infrastructure:
- **Database**: Supabase PostgreSQL with migrations
- **Authentication**: Supabase Auth with OAuth providers
- **Storage**: Supabase Storage for files/images
- **Backend**: Optional FastAPI backend (hybrid mode)
- **Frontend**: React Native app via Expo Application Services (EAS)

## 📋 Pre-Deployment Checklist

Before making any deployment updates:

- [ ] Test changes thoroughly in local development
- [ ] Run all tests (backend and frontend)
- [ ] Backup production database (if critical changes)
- [ ] Review migration scripts for data safety
- [ ] Verify environment variables are up to date
- [ ] Check OAuth provider configurations
- [ ] Ensure proper error handling for new features

## 🗄️ Database Changes & Migrations

### Understanding Supabase Migrations

Supabase uses SQL migration files to manage database schema changes:
- **Location**: `supabase/migrations/`
- **Format**: `YYYYMMDDHHMMSS_description.sql`
- **Execution**: Sequential, one-time only per environment

### Creating Database Migrations

#### 1. Create New Migration

```bash
# Create a new migration file
supabase migration new add_user_preferences_table

# This creates: supabase/migrations/20240621120000_add_user_preferences_table.sql
```

#### 2. Write Migration SQL

Edit the generated migration file:

```sql
-- supabase/migrations/20240621120000_add_user_preferences_table.sql

-- Create user preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    theme VARCHAR(20) DEFAULT 'light',
    notifications_enabled BOOLEAN DEFAULT true,
    language VARCHAR(10) DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

#### 3. Test Migration Locally

```bash
# Apply migration to local database
supabase db push

# Verify in Supabase Studio
open http://localhost:54323

# Test with sample data
supabase db reset  # This will re-run all migrations + seed data
```

#### 4. Validate Migration Safety

**Safe Migration Practices**:
- ✅ Adding new tables
- ✅ Adding new columns with DEFAULT values
- ✅ Creating indexes (use CONCURRENTLY in production)
- ✅ Adding constraints that don't conflict with existing data

**Potentially Unsafe Migrations**:
- ⚠️ Dropping columns (data loss)
- ⚠️ Changing column types (potential data loss)
- ⚠️ Adding NOT NULL constraints without defaults
- ⚠️ Dropping tables (data loss)

**For Unsafe Migrations**:
```sql
-- Example: Safe column type change
-- Step 1: Add new column
ALTER TABLE users ADD COLUMN email_new VARCHAR(255);

-- Step 2: Migrate data (in application code or separate script)
UPDATE users SET email_new = email WHERE email IS NOT NULL;

-- Step 3: In next migration, drop old column
ALTER TABLE users DROP COLUMN email;
ALTER TABLE users RENAME COLUMN email_new TO email;
```

### Deploying Database Changes

#### 1. Deploy to Staging (Recommended)

```bash
# Link to staging project
supabase link --project-ref your-staging-project-ref

# Push migrations to staging
supabase db push

# Test thoroughly in staging environment
```

#### 2. Deploy to Production

```bash
# Link to production project
supabase link --project-ref your-production-project-ref

# IMPORTANT: Backup production database first
# Go to Supabase Dashboard > Settings > Database > Database backups

# Push migrations to production
supabase db push

# Monitor for errors in Supabase Dashboard > Logs
```

#### 3. Rollback Strategy

If migration fails or causes issues:

```bash
# Option 1: Create rollback migration
supabase migration new rollback_user_preferences_table

# Option 2: Restore from backup (Supabase Dashboard)
# Go to Settings > Database > Database backups > Restore

# Option 3: Manual rollback (if safe)
# Connect to database and manually revert changes
```

## 🚀 Application Deployment

### Backend Deployment (FastAPI - Optional)

If using hybrid mode with custom FastAPI backend:

#### 1. Update Environment Variables

```bash
# Update production environment variables
# Platform-specific (Vercel, Railway, Render, etc.)

# Key variables to update:
SUPABASE_URL=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY=your-updated-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-updated-service-role-key
DATABASE_URL=postgresql://postgres:[PASSWORD]@db.your-project-ref.supabase.co:5432/postgres
```

#### 2. Deploy Backend

```bash
# Example for Railway
railway login
railway link your-project-id
railway up

# Example for Vercel
vercel --prod

# Example for Render
# Push to connected Git repository
git push origin main
```

### Frontend Deployment (React Native)

#### 1. Update App Configuration

Update `frontend/app.json` with production Supabase credentials:

```json
{
  "expo": {
    "extra": {
      "supabaseUrl": "https://your-project-ref.supabase.co",
      "supabaseAnonKey": "your-production-anon-key",
      "apiBaseUrl": "https://your-backend-url.com"
    }
  }
}
```

#### 2. Build and Deploy with EAS

```bash
cd frontend

# Install EAS CLI if not already installed
npm install -g @expo/eas-cli

# Login to Expo
eas login

# Configure build profiles (first time only)
eas build:configure

# Build for production
eas build --platform all --profile production

# Submit to app stores (when ready)
eas submit --platform ios --profile production
eas submit --platform android --profile production
```

#### 3. Over-the-Air (OTA) Updates

For JavaScript-only changes (no native code changes):

```bash
# Publish OTA update
eas update --branch production --message "Fix user preferences bug"

# Users will receive update automatically on next app launch
```

## 🔧 Configuration Updates

### OAuth Provider Updates

When updating OAuth configurations:

#### 1. Update Supabase Dashboard

1. Go to Supabase Dashboard > Authentication > Providers
2. Update client IDs, secrets, and redirect URLs
3. Test authentication flow in staging

#### 2. Update Application Environment Variables

```bash
# Backend .env
GOOGLE_CLIENT_ID=new-google-client-id
GOOGLE_CLIENT_SECRET=new-google-client-secret

# Frontend app.json or environment
```

#### 3. Update OAuth Provider Consoles

- **Google**: Update authorized redirect URIs
- **Apple**: Update service ID configuration
- **Microsoft**: Update redirect URIs in Azure AD

### Storage Configuration Updates

For Supabase Storage policy changes:

```sql
-- Example: Update storage policies
-- Create new migration file

-- Allow users to upload profile images
CREATE POLICY "Users can upload profile images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'profile-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Allow users to view their own images
CREATE POLICY "Users can view own profile images" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'profile-images' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );
```

## 📊 Monitoring & Rollback

### Post-Deployment Monitoring

#### 1. Database Monitoring

- **Supabase Dashboard**: Monitor query performance, errors
- **Logs**: Check for migration errors or constraint violations
- **Metrics**: Monitor connection count, query duration

#### 2. Application Monitoring

```bash
# Backend logs (platform-specific)
railway logs  # Railway
vercel logs   # Vercel

# Frontend crash reporting
# Check Expo dashboard for crash reports
```

#### 3. User Impact Assessment

- Monitor authentication success rates
- Check API error rates
- Verify critical user flows work correctly

### Emergency Rollback Procedures

#### 1. Database Rollback

```bash
# Option 1: Restore from backup
# Supabase Dashboard > Settings > Database > Restore backup

# Option 2: Create rollback migration
supabase migration new emergency_rollback_YYYYMMDD
# Write SQL to undo changes

# Option 3: Manual intervention
# Connect to database and manually fix issues
```

#### 2. Application Rollback

```bash
# Backend: Redeploy previous version
git revert HEAD
git push origin main

# Frontend: Rollback OTA update
eas update --branch production --message "Rollback to previous version"

# Or republish previous build
eas build --platform all --profile production
```

## 🔒 Security Considerations

### Database Security

- Always use Row Level Security (RLS) policies
- Validate migration scripts don't expose sensitive data
- Use service role key only in backend, never in frontend
- Regularly rotate API keys and database passwords

### Application Security

- Keep OAuth client secrets secure
- Use environment variables, never hardcode credentials
- Validate all user inputs in both frontend and backend
- Implement proper error handling to avoid information leakage

## 📚 Best Practices Summary

1. **Always test migrations locally first**
2. **Use staging environment for validation**
3. **Backup production database before major changes**
4. **Monitor deployments closely**
5. **Have rollback plan ready**
6. **Update documentation with changes**
7. **Communicate changes to team/users**
8. **Use semantic versioning for releases**

## 🆘 Emergency Contacts & Resources

- **Supabase Status**: https://status.supabase.com/
- **Supabase Support**: Dashboard > Help & Support
- **Expo Status**: https://status.expo.dev/
- **Documentation**: 
  - [Supabase Migrations](https://supabase.com/docs/guides/cli/local-development#database-migrations)
  - [EAS Build](https://docs.expo.dev/build/introduction/)
  - [EAS Update](https://docs.expo.dev/eas-update/introduction/)

Remember: When in doubt, test in staging first! 🛡️

# Customizable Database Implementation

This document describes the implementation of a Notion-style customizable database system for the WhereZ application.

## Overview

The implementation allows users to create custom databases with configurable fields and store records with dynamic properties. It supports advanced filtering, search, and data management capabilities similar to Notion's database functionality.

## Database Schema

### Core Tables

1. **`databases`** - Container for user-defined databases
   - `id` (UUID) - Primary key
   - `owner_id` (UUID) - References users.id
   - `name` (VARCHAR) - Database name
   - `description` (TEXT) - Optional description
   - `icon` (VARCHAR) - Emoji or icon identifier
   - `color` (VARCHAR) - Hex color code
   - Timestamps: `created_at`, `updated_at`

2. **`fields`** - Field definitions (schema metadata)
   - `id` (UUID) - Primary key
   - `database_id` (UUID) - References databases.id
   - `name` (VARCHAR) - Field name
   - `type` (ENUM) - Field type (text, number, date, select, etc.)
   - `settings` (JSONB) - Field-specific configuration
   - `position` (INTEGER) - Display order
   - Timestamps: `created_at`, `updated_at`

3. **`records`** - Record storage with JSONB properties
   - `id` (UUID) - Primary key
   - `database_id` (UUID) - References databases.id
   - `properties` (JSONB) - Dynamic properties by field ID
   - `created_by` (UUID) - References users.id
   - Timestamps: `created_at`, `updated_at`

### Field Types Supported

- **text** - Plain text with optional max length
- **number** - Numeric values with min/max constraints
- **date** - Date/datetime values
- **select** - Single selection from predefined options
- **multi_select** - Multiple selections from predefined options
- **checkbox** - Boolean values
- **url** - URL validation
- **email** - Email validation
- **phone** - Phone number
- **rich_text** - Rich text content

### Indexing Strategy

- **GIN Index** on `properties` JSONB column for fast containment queries
- **Full-text Search** using generated `tsvector` column
- **Standard B-tree indexes** on foreign keys and commonly queried fields

## API Endpoints

### Database Management

- `POST /databases` - Create new database
- `GET /databases` - List user databases with pagination
- `GET /databases/{id}` - Get database details with fields
- `PUT /databases/{id}` - Update database metadata
- `DELETE /databases/{id}` - Delete database and all data
- `POST /databases/{id}/clone` - Clone database structure/data
- `GET /databases/stats` - Get database statistics
- `GET /databases/search` - Search databases by name/description

### Field Management

- `POST /databases/{id}/fields` - Create new field
- `GET /databases/{id}/fields` - List database fields
- `GET /databases/{id}/fields/{field_id}` - Get field details
- `PUT /databases/{id}/fields/{field_id}` - Update field
- `DELETE /databases/{id}/fields/{field_id}` - Delete field
- `POST /databases/{id}/fields/reorder` - Reorder fields
- `PUT /databases/{id}/fields/bulk` - Bulk update fields
- `POST /databases/{id}/fields/{field_id}/validate` - Validate field value

### Record Management

- `POST /databases/{id}/records` - Create new record
- `GET /databases/{id}/records` - List records with pagination
- `POST /databases/{id}/records/filter` - Advanced filtering and search
- `GET /databases/{id}/records/{record_id}` - Get record details
- `PUT /databases/{id}/records/{record_id}` - Update record
- `DELETE /databases/{id}/records/{record_id}` - Delete record
- `POST /databases/{id}/records/bulk` - Bulk create records
- `PUT /databases/{id}/records/bulk` - Bulk update records
- `DELETE /databases/{id}/records/bulk` - Bulk delete records
- `POST /databases/{id}/records/export` - Export records (CSV/JSON)

## Advanced Features

### Filtering System

The filtering system supports multiple operators:

- **Text**: equals, not_equals, contains, not_contains, starts_with, ends_with
- **Numeric**: equals, not_equals, greater_than, less_than, greater_equal, less_equal
- **General**: is_empty, is_not_empty, in, not_in

### Full-text Search

- Automatic indexing of all record properties
- PostgreSQL's built-in full-text search capabilities
- Search across all text content in records

### Data Validation

- Type-specific validation for all field types
- Required field validation
- Custom constraints (min/max values, options, etc.)
- Bulk validation for import operations

### Export/Import

- CSV and JSON export formats
- Field name mapping for human-readable exports
- Bulk import with validation
- Error reporting for failed imports

## Security

### Row Level Security (RLS)

All tables have RLS policies ensuring:
- Users can only access their own databases
- Users can only manage fields in their databases
- Users can only create/modify records in their databases
- Proper cascade deletion with foreign key constraints

### Input Validation

- Comprehensive Pydantic schemas for all API inputs
- SQL injection prevention through parameterized queries
- Type validation and sanitization
- Rate limiting considerations for bulk operations

## Performance Considerations

### Database Optimization

- JSONB GIN indexing for fast property queries
- Generated tsvector column for full-text search
- Proper foreign key indexing
- Connection pooling and query optimization

### API Optimization

- Pagination for large datasets
- Bulk operations to reduce round trips
- Lazy loading of related data
- Caching strategies for frequently accessed data

## Usage Examples

### Creating a Database

```python
# Create a new database
database_data = {
    "name": "Project Tracker",
    "description": "Track project progress and tasks",
    "icon": "📋",
    "color": "#3B82F6"
}
response = requests.post("/databases", json=database_data)
```

### Adding Fields

```python
# Add a text field
field_data = {
    "name": "Project Name",
    "type": "text",
    "settings": {"required": True, "max_length": 100},
    "position": 0
}
response = requests.post(f"/databases/{database_id}/fields", json=field_data)

# Add a select field
field_data = {
    "name": "Status",
    "type": "select",
    "settings": {
        "required": True,
        "options": ["Not Started", "In Progress", "Completed"]
    },
    "position": 1
}
response = requests.post(f"/databases/{database_id}/fields", json=field_data)
```

### Creating Records

```python
# Create a record
record_data = {
    "properties": {
        "field_id_1": "My Project",
        "field_id_2": "In Progress"
    }
}
response = requests.post(f"/databases/{database_id}/records", json=record_data)
```

### Advanced Filtering

```python
# Filter records
filter_request = {
    "filters": [
        {
            "field_id": "status_field_id",
            "operator": "equals",
            "value": "In Progress"
        }
    ],
    "search": "important project",
    "sort_by": "created_at",
    "sort_order": "desc",
    "page": 1,
    "page_size": 20
}
response = requests.post(f"/databases/{database_id}/records/filter", json=filter_request)
```

## Testing

The implementation includes comprehensive test coverage:

- Unit tests for all service methods
- Integration tests for API endpoints
- Validation tests for all field types
- Performance tests for bulk operations
- Security tests for RLS policies

## Future Enhancements

Potential improvements and extensions:

1. **Relationships** - Link records between databases
2. **Formulas** - Calculated fields with expressions
3. **Views** - Saved filter/sort configurations
4. **Templates** - Pre-configured database templates
5. **Collaboration** - Sharing and permissions
6. **Webhooks** - Real-time notifications
7. **API Keys** - External integrations
8. **Advanced Charts** - Data visualization

## Migration Guide

To apply the database changes:

1. The schema has been updated in `supabase/migrations/20240101000000_initial_schema.sql`
2. Run `supabase db reset` to apply the new schema
3. The backend models and API endpoints are ready to use
4. Frontend integration can begin using the documented API endpoints

This implementation provides a solid foundation for a Notion-style database system with room for future enhancements and customizations.
